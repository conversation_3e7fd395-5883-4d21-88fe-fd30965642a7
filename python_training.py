"""
import pandas as pd

df = pd.DataFrame({
    "name": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
    "weight": [84, 79, 67, 52],
    "height": [183, 186, 158, 155],
    "smoker": ["no", "yes", "no", "no"]
})
# print(df.loc[df['weight'] > 70])
# print(df.iloc[1::2])

df['weight_in_pounds'] = df['weight'].apply(lambda x: x * 2.2)

df['smoker_bool'] = df['smoker'].map({"yes": True, "no": False})
print(df)

# df['gender'] = ['male', 'male', 'female', 'female']
# grouped = df.groupby('gender')[['weight', 'height']].mean()
# print(grouped)

# for index, row in df.iterrows():
#     print(f"Index: {index}, Name: {row['name']}, weight: {row['weight']} ")

for rows in df.itertuples(index=True):
    print(f"Index: {rows.Index}, Name: {rows.name}, Weight: {rows.weight}")
"""
"""
def decorator(func):
    def wrapper():
        print("Before")
        func()
        print("After")
    return wrapper

@decorator
def greet():
    print("Hello")

print(greet())
"""

"""
def gen():
    for i in range(3):
        yield i

for val in gen():
    print(val)
"""

"""
import copy

l = [1, 2, [3, 4]]

shallow = copy.copy(l)
deep = copy.deepcopy(l)

deep[0] = 100
deep[2][0] = 300

print(l)
print(deep)

shallow[0] = 100
shallow[2][0] = 300

print(l)
print(shallow)
"""
"""
person = {
    'name': 'Alice',
    'age': 30,
    'address': {
        'city': 'New York',
        'zip': 10001,
        'geo': {
            'lat': 40.7128,
            'lng': -74.0060
        }
    },
    'skills': ['Python', 'SQL']
}

res = {}

while True:
    is_dict_present = False
    for key, val in person.items():
        if isinstance(val, dict):
            res = val
            is_dict_present = True
            person = val
    if not is_dict_present:
        break
print(res)

"""

"""def factorial(n, cache):
    for i in range(2, n + 1):
        if i not in cache:
            cache[i] = i * cache[i - 1]
    return cache[n]
    
cache = {}
cache[0] = 1
cache[1] = 1
print(factorial(1500, cache))"""

from multiprocessing import Pool

def print_number(n):
    print(n)

if __name__ == "__main__":
    with Pool() as pool:
        pool.map(print_number, range(1, 101))