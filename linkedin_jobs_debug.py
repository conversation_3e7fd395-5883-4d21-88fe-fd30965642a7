from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# Connect to existing Chrome with debugging enabled
options = webdriver.ChromeOptions()
options.debugger_address = "localhost:9222"
driver = webdriver.Chrome(options=options)

wait = WebDriverWait(driver, 15)

# Step 1: Go to LinkedIn Jobs page
driver.get("https://www.linkedin.com/jobs/")
wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input[aria-label='Search jobs']")))

# Step 2: Type a job title in search box and hit Enter
search_input = driver.find_element(By.CSS_SELECTOR, "input[aria-label='Search jobs']")
search_input.clear()
search_input.send_keys("Software Engineer")
search_input.send_keys(Keys.RETURN)
time.sleep(5)

# Step 3: Open the "Date Posted" filter
filter_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(@aria-label, 'Date posted filter')]")))
filter_button.click()

# Step 4: Select "Past 24 hours"
past_24_hours_option = wait.until(EC.element_to_be_clickable((By.XPATH, "//label[contains(., 'Past 24 hours')]")))
past_24_hours_option.click()

# Step 5: Click "Show results"
apply_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Show results')]")))
apply_button.click()
time.sleep(5)

# Step 6: Change URL filter from r86400 (24h) to r3600 (1h)
current_url = driver.current_url
if 'f_TPR' in current_url and 'r86400' in current_url:
    new_url = current_url.replace('r86400', 'r3600')
    print(f"Redirecting to: {new_url}")
    driver.get(new_url)
else:
    print("Expected time filter not found in URL.")

# Optional: keep tab open for inspection
time.sleep(10)
