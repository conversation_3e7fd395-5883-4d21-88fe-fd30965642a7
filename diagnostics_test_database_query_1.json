{"database_name": "test_database", "query": "SELECT u.id, u.name, u.email, o.amount, o.order_date\n        FROM users u\n        JOIN orders o ON u.id = o.user_id\n        WHERE o.order_date >= '2024-01-01'\n        ORDER BY o.amount DESC", "execution_time": 0.0007846355438232422, "explain_plan": [{"id": 1, "select_type": "SIMPLE", "table": "u", "partitions": null, "type": "ALL", "possible_keys": "PRIMARY", "key": null, "key_len": null, "ref": null, "rows": 1, "filtered": 100.0, "extra": "Using temporary; Using filesort"}, {"id": 1, "select_type": "SIMPLE", "table": "o", "partitions": null, "type": "ref", "possible_keys": "idx_user_id,idx_order_date", "key": "idx_user_id", "key_len": "4", "ref": "ecommerce_test.u.id", "rows": 1, "filtered": 100.0, "extra": "Using where"}], "warnings": ["Full table scan on table 'u' (rows: 1)", "Filesort operation detected on table 'u'", "Temporary table created for table 'u'", "No index used for table 'u'"], "recommendations": ["Consider adding an index on table 'u'", "Consider adding an index to avoid filesort on table 'u'", "Consider optimizing GROUP BY or ORDER BY clauses", "Consider adding appropriate indexes for table 'u'"], "timestamp": "2025-12-11T19:55:49.913734"}