
"""
Universal Video Concatenation Tool

This script provides multiple methods for video concatenation:
1. OpenCV (reliable, basic functionality)
2. MoviePy (advanced features, if available)
3. FFmpeg (command-line, if available)

The script automatically detects which method is available and uses the best one.
"""

import os
import sys
import glob
import subprocess

def check_dependencies():
    """Check which video processing libraries are available."""
    available = {}
    
    # Check OpenCV
    try:
        import cv2
        available['opencv'] = cv2.__version__
        print(f"✅ OpenCV available (version {cv2.__version__})")
    except ImportError:
        available['opencv'] = None
        print("❌ OpenCV not available")
    
    # Check MoviePy
    try:
        import moviepy
        from moviepy.editor import VideoFileClip, concatenate_videoclips
        available['moviepy'] = moviepy.__version__
        print(f"✅ MoviePy available (version {moviepy.__version__})")
    except ImportError:
        available['moviepy'] = None
        print("❌ MoviePy not available")
    
    # Check FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            available['ffmpeg'] = version_line
            print(f"✅ FFmpeg available ({version_line})")
        else:
            available['ffmpeg'] = None
    except (subprocess.SubprocessError, FileNotFoundError):
        available['ffmpeg'] = None
        print("❌ FFmpeg not available")
    
    return available

def concatenate_with_opencv(video_paths, output_path, target_fps=30, verbose=True):
    """Concatenate videos using OpenCV."""
    import cv2
    import numpy as np
    
    if verbose:
        print("Using OpenCV for concatenation...")
    
    # Get dimensions from first video
    cap = cv2.VideoCapture(video_paths[0])
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap.release()
    
    # Create output video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, target_fps, (width, height))
    
    total_frames = 0
    
    for i, video_path in enumerate(video_paths):
        if verbose:
            print(f"Processing {i+1}/{len(video_paths)}: {os.path.basename(video_path)}")
        
        cap = cv2.VideoCapture(video_path)
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Resize if necessary
            if frame.shape[1] != width or frame.shape[0] != height:
                frame = cv2.resize(frame, (width, height))
            
            out.write(frame)
            frame_count += 1
            total_frames += 1
        
        cap.release()
        if verbose:
            print(f"  Added {frame_count} frames")
    
    out.release()
    
    if verbose:
        print(f"✅ Concatenation complete! Total frames: {total_frames}")
    
    return True

def concatenate_with_moviepy(video_paths, output_path, verbose=True):
    """Concatenate videos using MoviePy."""
    from moviepy.editor import VideoFileClip, concatenate_videoclips
    
    if verbose:
        print("Using MoviePy for concatenation...")
    
    clips = [VideoFileClip(path) for path in video_paths]
    final_clip = concatenate_videoclips(clips, method="compose")
    final_clip.write_videofile(output_path, verbose=verbose, logger='bar' if verbose else None)
    
    # Clean up
    for clip in clips:
        clip.close()
    final_clip.close()
    
    return True

def concatenate_with_ffmpeg(video_paths, output_path, verbose=True):
    """Concatenate videos using FFmpeg."""
    if verbose:
        print("Using FFmpeg for concatenation...")
    
    # Create a temporary file list for FFmpeg
    list_file = "temp_video_list.txt"
    
    try:
        with open(list_file, 'w') as f:
            for video_path in video_paths:
                f.write(f"file '{os.path.abspath(video_path)}'\n")
        
        # Run FFmpeg command
        cmd = [
            'ffmpeg', '-f', 'concat', '-safe', '0', '-i', list_file,
            '-c', 'copy', output_path, '-y'
        ]
        
        if verbose:
            result = subprocess.run(cmd, text=True)
        else:
            result = subprocess.run(cmd, capture_output=True, text=True)
        
        return result.returncode == 0
    
    finally:
        # Clean up temporary file
        if os.path.exists(list_file):
            os.remove(list_file)

def concatenate_videos_universal(video_paths, output_path, method='auto', target_fps=30, verbose=True):
    """
    Universal video concatenation function that uses the best available method.
    
    Args:
        video_paths (list): List of video file paths
        output_path (str): Output video path
        method (str): 'auto', 'opencv', 'moviepy', or 'ffmpeg'
        target_fps (int): Target FPS for OpenCV method
        verbose (bool): Whether to show progress
    
    Returns:
        bool: True if successful
    """
    if not video_paths:
        print("Error: No video files provided")
        return False
    
    # Check dependencies
    available = check_dependencies()
    
    # Determine method to use
    if method == 'auto':
        if available['ffmpeg']:
            method = 'ffmpeg'
        elif available['moviepy']:
            method = 'moviepy'
        elif available['opencv']:
            method = 'opencv'
        else:
            print("❌ No video processing libraries available!")
            print("Install one of: opencv-python, moviepy, or ffmpeg")
            return False
    
    # Validate chosen method
    if method == 'opencv' and not available['opencv']:
        print("❌ OpenCV not available. Install with: pip install opencv-python")
        return False
    elif method == 'moviepy' and not available['moviepy']:
        print("❌ MoviePy not available. Install with: pip install moviepy")
        return False
    elif method == 'ffmpeg' and not available['ffmpeg']:
        print("❌ FFmpeg not available. Install FFmpeg and add to PATH")
        return False
    
    if verbose:
        print(f"📹 Concatenating {len(video_paths)} videos using {method.upper()}")
        for i, path in enumerate(video_paths, 1):
            print(f"  {i}. {os.path.basename(path)}")
    
    try:
        if method == 'opencv':
            return concatenate_with_opencv(video_paths, output_path, target_fps, verbose)
        elif method == 'moviepy':
            return concatenate_with_moviepy(video_paths, output_path, verbose)
        elif method == 'ffmpeg':
            return concatenate_with_ffmpeg(video_paths, output_path, verbose)
    except Exception as e:
        print(f"❌ Error during concatenation: {e}")
        return False

def get_video_files(directory, extensions=None, remove_duplicates=True):
    """Get all video files from a directory."""
    if extensions is None:
        extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.wmv', '*.flv', '*.webm']

    video_files = []
    for ext in extensions:
        video_files.extend(glob.glob(os.path.join(directory, ext)))
        video_files.extend(glob.glob(os.path.join(directory, ext.upper())))

    # Remove duplicates while preserving order
    if remove_duplicates:
        seen = set()
        unique_files = []
        for file in sorted(video_files):
            if file not in seen:
                seen.add(file)
                unique_files.append(file)
        video_files = unique_files
    else:
        video_files = sorted(video_files)

    return video_files

def main():
    """Main function for command-line usage."""
    print("🎬 Universal Video Concatenation Tool")
    print("=" * 50)
    
    # Check what's available
    available = check_dependencies()
    print()
    
    # Get input
    if len(sys.argv) > 1:
        video_dir = sys.argv[1]
    else:
        video_dir = input("Enter directory containing videos: ").strip()
    
    if not os.path.isdir(video_dir):
        print(f"❌ Directory '{video_dir}' not found")
        return
    
    video_files = get_video_files(video_dir, remove_duplicates=True)
    if not video_files:
        print("❌ No video files found")
        return

    print(f"\n📁 Found {len(video_files)} unique videos:")
    for i, file in enumerate(video_files, 1):
        print(f"  {i}. {os.path.basename(file)}")
    
    # Get output filename
    output_name = input("\nOutput filename (default: concatenated.mp4): ").strip()
    if not output_name:
        output_name = "concatenated.mp4"
    
    if not output_name.endswith('.mp4'):
        output_name += '.mp4'
    
    output_path = os.path.join(video_dir, output_name)
    
    # Choose method
    print("\nAvailable methods:")
    methods = []
    if available['ffmpeg']:
        methods.append('ffmpeg')
        print("  1. FFmpeg (fastest, best quality)")
    if available['moviepy']:
        methods.append('moviepy')
        print(f"  {len(methods)+1}. MoviePy (advanced features)")
    if available['opencv']:
        methods.append('opencv')
        print(f"  {len(methods)+1}. OpenCV (reliable)")
    
    if not methods:
        print("❌ No methods available!")
        return
    
    choice = input(f"\nChoose method (1-{len(methods)}, default: auto): ").strip()
    
    if choice.isdigit() and 1 <= int(choice) <= len(methods):
        method = methods[int(choice)-1]
    else:
        method = 'auto'
    
    # Concatenate
    print(f"\n🚀 Starting concatenation...")
    success = concatenate_videos_universal(video_files, output_path, method=method)
    
    if success:
        print(f"\n🎉 Success! Output saved as: {output_name}")
    else:
        print("\n❌ Concatenation failed")

if __name__ == "__main__":
    main()

