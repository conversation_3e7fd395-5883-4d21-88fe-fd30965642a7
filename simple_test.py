#!/usr/bin/env python3

# Simple test to check if imports work
import sys
import os

# Add the current directory to Python path
sys.path.append(os.getcwd())

try:
    from BNPL.service.UserService import UserService
    print("✅ Import successful!")
    
    # Test basic functionality
    user_service = UserService()
    user = user_service.register_user("test_user", 1000)
    print(f"✅ User registered: {user.user_id}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
