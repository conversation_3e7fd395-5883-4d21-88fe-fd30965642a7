{"test_database": {"database_name": "test_database", "query": "SELECT u.name, o.amount FROM users u JOIN orders o ON u.id = o.user_id WHERE o.status = 'completed'", "execution_time": 0.0010995864868164062, "explain_plan": [{"id": 1, "select_type": "SIMPLE", "table": "u", "partitions": null, "type": "ALL", "possible_keys": "PRIMARY", "key": null, "key_len": null, "ref": null, "rows": 1, "filtered": 100.0, "extra": null}, {"id": 1, "select_type": "SIMPLE", "table": "o", "partitions": null, "type": "ref", "possible_keys": "idx_user_id,idx_status", "key": "idx_user_id", "key_len": "4", "ref": "ecommerce_test.u.id", "rows": 1, "filtered": 100.0, "extra": "Using where"}], "warnings": ["Full table scan on table 'u' (rows: 1)", "No index used for table 'u'"], "recommendations": ["Consider adding an index on table 'u'", "Consider adding appropriate indexes for table 'u'"], "timestamp": "2025-12-11T19:54:22.524483"}}