from collections import defaultdict


class VendingMachine(object):
    def __init__(self):
        self.products = defaultdict(list)
        self.denomenation = {1:0, 2:0, 5:0}

    def add_product(self, product, quantity, price):
        if quantity > 0 and price > 0:  
            self.products[product] = [quantity, price]
        else:
            print(f"{quantity} and {price} should be greater than 0.")

    def update_price(self, product, price):
        if product in self.products:
            if price > 0:
                self.products[product][1] = price
                print(f"{product} price updated in Vending Machine.")
            else:
                print("Price should be greater than 0.")
        else:
            print(f"{product} not in Vending Machine.")

    def available_items(self):
        for product, details in self.products.items():
            quantity, price = details
            print(f"Product - {product}, Quantity - {quantity}")

    def restock_items(self, product, quantity):
        if product in self.products:
            if quantity > 0:
                self.products[product][0] += quantity
                print(f"{product} restocked in Vending Machine.")
            else:
                print("Quantity should be greater than 0.")
        else:
            print(f"{product} not in Vending Machine.")

    def restock_money(self, coins):
        for coin, qunatity in coins.items():
            if coin in self.denomenation:
                self.denomenation[coin] += qunatity
            else:
                print(f"{coin} not in {self.denomenation}")
    
    def transaction(self, payment, cost):
        total_paid = 0
        for coin, value in payment.items():
            if coin in self.denomenation:
                total_paid += (coin * value)
                self.denomenation[coin] += value
            else:
                return f"{coin} not in machine."
            
        if total_paid < cost:
            return "Insufficient payment."
        
        change = total_paid - cost
        if not self.provide_change(change):
            for coin, value in payment.items():
                    self.denomenation[coin] -= value
            return "Unable to provide exact change. Transaction Failed."
        
        return change
    
    def provide_change(self, change):
        temp_denominations = self.denomenation.copy()

        for coin in sorted(self.denomenation.keys(), reverse=True):
            while change >= coin and temp_denominations[coin] > 0:
                change -= coin
                temp_denominations[coin] -= 1

        if change == 0:
            self.denomenation = temp_denominations
            return True
        else:
            return False

    def order(self, product, quantity, payment):
        if product in self.products:
            available_quantity, price = self.products[product]
            if quantity <= available_quantity:
                total_cost = price * quantity
                change = self.transaction(payment, total_cost)
                if isinstance(change, str):
                    print(change)
                else:
                    self.products[product][0] -= quantity
                    print(f"Order successful. Change returned: {change}")
            else:
                print("Insufficient quantity in vending machine.")
        else:
            print("Product not in vending machine.")

vm = VendingMachine()
vm.add_product("A", 2, 5)
vm.add_product("B", 1, 10)
vm.available_items()
vm.update_price("A", -6)
vm.order("A", 1, {1:6})
vm.available_items()
vm.restock_items("F", 8)
vm.available_items()
vm.order("B", 1, {5:2})
vm.order("B", 1, {5:2})
print(vm.denomenation)