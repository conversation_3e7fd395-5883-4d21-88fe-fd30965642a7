#!/usr/bin/env python3
"""
Standalone BNPL System - All classes in one file for easy execution
"""

from enum import Enum
from datetime import date
from typing import List, Tuple, Optional, Dict

# PaymentMethod Enum
class PaymentMethod(str, Enum):
    BNPL = 'BNPL'
    PREPAID = 'PREPAID'

# Models
class Item:
    def __init__(self, name: str, price: float, count: int):
        self.name = name
        self.price = price
        self.count = count

    def __str__(self):
        return f"Item(name={self.name}, price={self.price}, count={self.count})"

class User:
    def __init__(self, user_id: str, credit_limit: float):
        self.user_id = user_id
        self.credit_limit = credit_limit
        self.remaining_limit = credit_limit
        self.orders = []

    def __str__(self):
        return f"User(id={self.user_id}, credit_limit={self.credit_limit}, remaining={self.remaining_limit})"

class Order:
    def __init__(self, order_id: str, user_id: str, items: List[Tuple[str, int]],
                 total_amount: float, payment_method: PaymentMethod, purchase_date: date):
        self.order_id = order_id
        self.user_id = user_id
        self.items = items
        self.total_amount = total_amount
        self.payment_method = payment_method
        self.purchase_date = purchase_date
        self.cleared = False
        self.cleared_date: Optional[date] = None

    def is_due(self, on_date: date) -> bool:
        return (
            self.payment_method == PaymentMethod.BNPL
            and not self.cleared
            and self.purchase_date <= on_date
        )

    def status_on(self, on_date: date) -> str:
        if self.payment_method == PaymentMethod.PREPAID:
            return "PAID"
        elif self.cleared:
            return "CLEARED"
        elif self.purchase_date <= on_date:
            return "DUE"
        else:
            return "FUTURE"

    def __str__(self):
        return f"Order(id={self.order_id}, user={self.user_id}, amount={self.total_amount}, method={self.payment_method})"

# Services
class UserService:
    def __init__(self):
        self.users: Dict[str, User] = {}

    def register_user(self, user_id: str, credit_limit: float) -> User:
        if user_id in self.users:
            raise ValueError(f"User {user_id} already exists")
        
        user = User(user_id, credit_limit)
        self.users[user_id] = user
        return user

    def get_user(self, user_id: str) -> User:
        if user_id not in self.users:
            raise ValueError(f"User {user_id} not found")
        return self.users[user_id]

class ItemService:
    def __init__(self, item_repository: Dict[str, Item] = None):
        self.item_repository = item_repository or {}

    def seed_inventory(self, items: List[Tuple[str, int, float]]) -> None:
        for name, count, price in items:
            self.item_repository[name] = Item(name=name, price=price, count=count)

    def view_inventory(self) -> None:
        if not self.item_repository:
            print("Inventory is empty")
            return
        
        print("\n--- INVENTORY ---")
        for item in self.item_repository.values():
            print(f"{item.name}: price=${item.price}, stock={item.count}")

    def get_item(self, item_name: str) -> Item:
        if item_name not in self.item_repository:
            raise ValueError(f"Item {item_name} not found")
        return self.item_repository[item_name]
    
    def check_availability(self, item_name: str, quantity: int) -> bool:
        if item_name not in self.item_repository:
            return False
        return self.item_repository[item_name].count >= quantity
    
    def calculate_total(self, cart: List[Tuple[str, int]]) -> float:
        total = 0.0
        for item_name, quantity in cart:
            item = self.get_item(item_name)
            total += item.price * quantity
        return total
    
    def validate_and_reserve_items(self, cart: List[Tuple[str, int]]) -> None:
        # First validate all items
        for item_name, quantity in cart:
            if not self.check_availability(item_name, quantity):
                raise ValueError(f"Insufficient stock for {item_name}")
        
        # Then reserve items
        for item_name, quantity in cart:
            self.item_repository[item_name].count -= quantity

class OrderService:
    def __init__(self, user_service, item_service):
        self.user_service = user_service
        self.item_service = item_service
        self._next_order_id = 1

    def place_order(self, user_id: str, cart: List[Tuple[str, int]],
                   payment_method: str, purchase_date: str) -> Order:
        # Convert string inputs to proper types
        payment_method_enum = PaymentMethod.BNPL if payment_method == "BNPL" else PaymentMethod.PREPAID
        purchase_date_obj = date.fromisoformat(purchase_date) if isinstance(purchase_date, str) else purchase_date

        # Get user
        user = self.user_service.get_user(user_id)

        # Validate and calculate total
        total_amount = self.item_service.calculate_total(cart)

        # Check BNPL credit if needed
        if payment_method_enum == PaymentMethod.BNPL:
            if total_amount > user.remaining_limit:
                raise ValueError("Not enough BNPL credit")

        # Reserve items
        self.item_service.validate_and_reserve_items(cart)

        # Create order
        order_id = str(self._next_order_id)
        order = Order(
            order_id=order_id,
            user_id=user_id,
            items=cart,
            total_amount=total_amount,
            payment_method=payment_method_enum,
            purchase_date=purchase_date_obj
        )

        # Update user credit if BNPL
        if payment_method_enum == PaymentMethod.BNPL:
            user.remaining_limit -= total_amount

        # Store order
        user.orders.append(order)
        self._next_order_id += 1

        print(f"\nOrder placed successfully. ID = {order.order_id}, Amount = {total_amount}, By = {user_id}")
        return order

    def view_dues(self, user_id: str, upto_date: str) -> None:
        user = self.user_service.get_user(user_id)
        upto_date_obj = date.fromisoformat(upto_date) if isinstance(upto_date, str) else upto_date

        dues = [o for o in user.orders if o.is_due(upto_date_obj)]
        dues.sort(key=lambda o: o.purchase_date)

        print(f"\nDUES for {user_id} upto {upto_date}")
        if not dues:
            print("\nNo dues.")
            return

        for o in dues:
            print(
                f"OrderID={o.order_id} | Amount={o.total_amount} | "
                f"Date={o.purchase_date} | Status={o.status_on(upto_date_obj)}"
            )

    def clear_dues(self, user_id: str, order_ids: List[str], clearing_date: str) -> None:
        user = self.user_service.get_user(user_id)
        clearing_date_obj = date.fromisoformat(clearing_date) if isinstance(clearing_date, str) else clearing_date

        for oid in order_ids:
            orders = {o.order_id: o for o in user.orders}
            order = orders.get(oid)
            if not order:
                print(f"\nOrder {oid} not found")
                continue
            if order.cleared or order.payment_method != PaymentMethod.BNPL:
                print(f"No BNPL dues for Order {oid}")
                continue

            order.cleared = True
            order.cleared_date = clearing_date_obj
            user.remaining_limit += order.total_amount
            print(f"\nDues cleared for order {oid}")

    def order_status(self, user_id: str) -> None:
        user = self.user_service.get_user(user_id)
        print(f"\nORDER HISTORY for {user_id}")

        if not user.orders:
            print("No orders found.")
            return

        for o in sorted(user.orders, key=lambda x: x.purchase_date):
            print(
                f"OrderID={o.order_id} | Date={o.purchase_date} | "
                f"Payment={o.payment_method} | Amount={o.total_amount} | Cleared={o.cleared}"
            )

        print(f"Current BNPL Credit Left: {user.remaining_limit}")

# Main Application
class Main:
    def __init__(self):
        self.item_service = ItemService()
        self.user_service = UserService()
        self.order_service = OrderService(self.user_service, self.item_service)

if __name__ == "__main__":
    main = Main()

    print("=== BNPL System Test ===")

    # 1. Seed Inventory
    print("\n--- 1. Seeding Inventory ---")
    main.item_service.seed_inventory([
        ("Laptop", 10, 500.0),
        ("Mobile", 20, 100.0),
        ("Headphones", 15, 20.0),
    ])
    main.item_service.view_inventory()

    # 2. Register Users
    print("\n--- 2. Registering Users ---")
    user1 = main.user_service.register_user("user1", credit_limit=1000)
    user2 = main.user_service.register_user("user2", credit_limit=2000)
    print(f"Registered: {user1}")
    print(f"Registered: {user2}")

    # 3. Place Orders
    print("\n--- 3. Placing Orders ---")
    cart1 = [("Laptop", 1), ("Headphones", 2)]
    order1 = main.order_service.place_order("user1", cart1, "BNPL", "2024-01-01")

    cart2 = [("Mobile", 2)]
    order2 = main.order_service.place_order("user2", cart2, "PREPAID", "2024-01-02")

    # 4. View Inventory After Orders
    print("\n--- 4. Inventory After Orders ---")
    main.item_service.view_inventory()

    # 5. View Dues
    print("\n--- 5. Viewing Dues ---")
    main.order_service.view_dues("user1", "2024-02-01")
    main.order_service.view_dues("user2", "2024-02-01")

    # 6. Clear Dues
    print("\n--- 6. Clearing Dues ---")
    main.order_service.clear_dues("user1", [order1.order_id], "2024-02-02")

    # 7. View Dues After Clearing
    print("\n--- 7. Dues After Clearing ---")
    main.order_service.view_dues("user1", "2024-02-03")

    # 8. Order Status
    print("\n--- 8. Order Status ---")
    main.order_service.order_status("user1")
    main.order_service.order_status("user2")

    print("\n=== Test Completed Successfully ===")
    print("🎉 All BNPL system components are working correctly!")
