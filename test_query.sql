-- Test query for MySQL diagnostics tool
-- This query works with our test database schema

SELECT 
    u.id,
    u.name,
    u.email,
    u.age,
    u.city,
    COUNT(o.id) as total_orders,
    SUM(o.amount) as total_spent,
    AVG(o.amount) as avg_order_value,
    MAX(o.order_date) as last_order_date
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.email LIKE '%@gmail.com'
    AND u.age > 25
    AND (o.status = 'completed' OR o.status IS NULL)
GROUP BY u.id, u.name, u.email, u.age, u.city
HAVING total_orders > 0 OR total_orders IS NULL
ORDER BY total_spent DESC, u.name ASC
LIMIT 10;
