package clearfit;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;

public class ClearFitService {
    private Map<String, Center> centers;
    private Map<String, User> users;

    public ClearFitService(){
        this.centers = new HashMap<>();
        this.users = new HashMap<>();
    }

    public Map<String, Center> getCenters() { return centers; }
    public Map<String, User> getUsers() { return users; }

    public boolean registerUser(String id, String name){
        if (!users.containsKey(id)){
            users.put(id, new User(id, name, new ArrayList<>()));
            return true;
        }
        return false;
    }

    public boolean registerCenter(String name, List<TimeSlot> timings,
    List<String> workoutType, Map<String,
    List<WorkoutSlot>> workoutSchedule){
        if (!centers.containsKey(name)){
            centers.put(name, new Center(name, timings, workoutType,
             workoutSchedule));
            return true;
        }
        return false;
    }

    public boolean addWorkoutSlot(String centername, String workoutType,
    TimeSlot timeslot, int capacity){
        Center center = centers.get(centername);
        if (center == null || 
        !center.getWorkoutType().contains(workoutType)) return false;

        center.getWorkoutSchedule().
        computeIfAbsent(workoutType, k -> new ArrayList<>())
        .add(new WorkoutSlot(capacity, capacity, 
        timeslot.getEndTime() - timeslot.getStartTime(), 
        timeslot, new HashSet<>()));
        return true;
    }

    public boolean bookSlot(String userId, String centername,
    String workoutType, TimeSlot timeslot){
        User user = users.get(userId);
        Center center = centers.get(centername);
        if (user == null || center == null) return false;

        List<WorkoutSlot> slots = center.getWorkoutSchedule().get(workoutType);
        if (slots == null) return false;

        for (WorkoutSlot slot: slots){
            if (slot.getTimeSlot().getStartTime() == timeslot.getStartTime()
            && slot.getTimeSlot().getEndTime() == timeslot.getEndTime()){
                synchronized(slot){
                    if(slot.bookedWorkoutSlot(userId)){
                        Booking booking = new Booking(user.getName(), centername, new WorkoutType(workoutType), timeslot, "BOOKED");
                        user.getBookings().add(booking);
                        return true;
                    }
                }
            }
        }
        return false;
    }
}
