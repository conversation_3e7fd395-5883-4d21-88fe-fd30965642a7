class Loan():
    def __init__(self, principal, rate, time):
        self.principal = principal
        self.rate = rate / (12 * 100)
        self.time = time * 12

    def loan_calc(self):
        emi = (self.principal * self.rate * pow(1 + self.rate, self.time)) / (pow(1 + self.rate, self.time)-1)
        return emi
    
if __name__ == "__main__":   
    while True:
        print("Menu")
        print("1. Calculate interest")
        print("2. Exit")
        choice = int(input("enter choice:"))
        if choice == 1:
            principal = float(input("enter principal amount:"))
            rate = float(input("enter rate:"))
            time = float(input("enter time:"))

            loan = Loan(principal, rate, time)

            print("Monthly EMI is {:.2f}".format(loan.loan_calc()))
        elif choice == 2:
            break
        else:
            print("Wrong choice")
