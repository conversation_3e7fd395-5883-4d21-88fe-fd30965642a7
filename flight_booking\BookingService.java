package flight_booking;
import java.util.*;

public class BookingService {
    private final FlightService flightService;
    private final Map<String, List<Booking>> userBookings;

    public BookingService(FlightService flightService){
        this.flightService = flightService;
        this.userBookings = new HashMap<>();
    }

    public String bookFlight(String userId, String flightNumber, String date){
        Flights flight = flightService.getFlight(flightNumber);

        if (flight == null){
            return "Flight not found: " + flightNumber;
        }

        synchronized (flight){
        if (!flight.getDate().equals(date)){
            return "Flight date mismatch for: " + flightNumber;
        }

        try{
            flight.bookSeat();
            Booking booking = new Booking(userId, flightNumber, date);

            userBookings.computeIfAbsent(userId, k -> new ArrayList<>()).add(booking);
            return "Booking confirmed for user " + userId + " for flight " + flightNumber;
        }
        catch (IllegalStateException e){
            return "Booking failed: " + e.getMessage();
        }
    }
    }

    public String cancelBooking(String userId, String flightNumber) {
        List<Booking> bookings = userBookings.getOrDefault(userId, new ArrayList<>());
        for (Booking b : bookings) {
            if (b.getFlightNumber().equals(flightNumber)) {
                bookings.remove(b);
                Flights flight = flightService.getFlight(flightNumber);
                if (flight != null) {
                    flight.cancelSeat(); // increase seat count
                }
                return "Booking cancelled for user " + userId + " on flight " + flightNumber;
            }
        }
        return "No booking found for user " + userId + " and flight " + flightNumber;
    }

    public void viewUserBookings(String userId) {
        List<Booking> bookings = userBookings.get(userId);
        if (bookings == null || bookings.isEmpty()) {
            System.out.println("No bookings found for user: " + userId);
            return;
        }
        for (Booking b : bookings) {
            System.out.println(b);
        }
    }

}
