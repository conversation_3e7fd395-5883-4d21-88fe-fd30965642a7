"""
Test script to check moviepy installation and imports.
"""

print("Testing moviepy installation...")

try:
    import moviepy
    print("✅ moviepy imported successfully")
    print(f"MoviePy version: {moviepy.__version__}")
except ImportError as e:
    print(f"❌ Failed to import moviepy: {e}")
    exit(1)

try:
    from moviepy.editor import VideoFileClip, concatenate_videoclips
    print("✅ moviepy.editor imported successfully")
    print("Available functions:", [name for name in dir() if 'Video' in name or 'concatenate' in name])
except ImportError as e:
    print(f"❌ Failed to import from moviepy.editor: {e}")
    print("Trying alternative import...")
    
    try:
        import moviepy.video.io.VideoFileClip as VideoFileClip
        import moviepy.video.compositing.concatenate as concatenate
        print("✅ Alternative import successful")
    except ImportError as e2:
        print(f"❌ Alternative import also failed: {e2}")
        exit(1)

print("\n🎬 MoviePy is ready for video processing!")
