# WE USE CHAIN OF RESPONSIBILITY IN THIS.
from abc import ABC, abstractmethod


class Cart:
    def __init__(self, prime_member, items):
        self.prime_member = prime_member
        self.items = items
        self.shipping_cost = None
        self.shipping_type = None
        self.total_amount = sum(item.price for item in items)
    
    def is_grocery_only(self):
        return all(item.is_grocery for item in self.items)

class Item:
    def __init__(self, name, price, subscribe_and_save=False, is_grocery=False):
        self.name = name
        self.price = price
        self.subscribe_and_save = subscribe_and_save
        self.is_grocery = is_grocery

class Handler(ABC):
    def __init__(self):
        self._next_handler = None

    def set_next(self, handler):
        self._next_handler = handler
        return handler
    
    @abstractmethod
    def handle(self, cart):
        if self._next_handler:
            return self._next_handler.handle(cart)
        
class FreeShippingHandler(Handler):
    def handle(self, cart):
        if cart.total_amount > 35 and cart.prime_member:
            cart.shipping_cost = 0
            cart.shipping_type = '2 day'
        return super().handle(cart)

class OneDayShippingHandler(Handler):
    def handle(self, cart):
        if cart.total_amount > 124:
            cart.shipping_cost = 0
            cart.shipping_type = '1 day'
        return super().handle(cart)

class TwoHourShippingHandler(Handler):
    def handle(self, cart):
        if cart.total_amount > 25 and cart.prime_member and cart.is_grocery_only:
            cart.shipping_cost = 0
            cart.shipping_type = '2 hour'
        return super().handle(cart)

class DiscountHandler(Handler):
    def handle(self, cart):
        for item in cart.items:
            if item.subscribe_and_save:
                item.price *= 0.9

        cart.total_amount = sum(item.price for item in cart.items)
        return super().handle(cart)

# EXAMPLE USAGE  
items = [
    Item('Milk', 3.5, subscribe_and_save=True, is_grocery=True),
    Item('Break', 2.5, is_grocery=True)
]

cart = Cart(prime_member=True, items=items)
# CREATE HANDLER
free_shipping_handler = FreeShippingHandler()
one_day_shipping_handler = OneDayShippingHandler()
two_hour_shipping_handler = TwoHourShippingHandler()
discount_handler = DiscountHandler()

# FORM THE CHAIN
free_shipping_handler.set_next(one_day_shipping_handler).set_next(two_hour_shipping_handler).set_next(discount_handler)

# START THE CHAIN
free_shipping_handler.handle(cart)
