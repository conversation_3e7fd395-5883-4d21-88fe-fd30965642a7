#!/usr/bin/env python3
"""
Example usage of MySQL Query Diagnostics Tool
"""

from mysql_query_diagnostics import MySQLQueryDiagnostics, DatabaseConfig

def main():
    # Initialize the diagnostics tool
    diagnostics = MySQLQueryDiagnostics()
    
    # Add database configurations
    # Test database (using our actual test database)
    test_config = DatabaseConfig(
        name="test_database",
        host="localhost",
        port=3306,
        database="ecommerce_test",
        user="root",
        password="Ashu.1998"
    )

    # Add configuration
    diagnostics.add_database(test_config)
    
    # Example queries to analyze (using our test database schema)
    queries = [
        # Query 1: Simple SELECT with potential performance issues
        """
        SELECT u.id, u.name, u.email, o.amount, o.order_date
        FROM users u
        JOIN orders o ON u.id = o.user_id
        WHERE o.order_date >= '2024-01-01'
        ORDER BY o.amount DESC
        """,

        # Query 2: Complex query with aggregation
        """
        SELECT
            u.city,
            COUNT(*) as user_count,
            SUM(o.amount) as total_revenue,
            AVG(o.amount) as avg_order_value
        FROM users u
        LEFT JOIN orders o ON u.id = o.user_id
        WHERE u.age > 25
        GROUP BY u.city
        HAVING total_revenue > 100
        ORDER BY total_revenue DESC
        """,

        # Query 3: Potentially problematic query (full table scan)
        """
        SELECT *
        FROM users
        WHERE email LIKE '%@gmail.com'
        """
    ]
    
    try:
        for i, query in enumerate(queries, 1):
            print(f"\n{'='*100}")
            print(f"ANALYZING QUERY {i}")
            print(f"{'='*100}")
            
            # Run diagnostics across all databases
            results = diagnostics.diagnose_across_databases(query)
            
            # Display results for each database
            for db_name, result in results.items():
                diagnostics.print_diagnostics_report(result)
            
            # Compare performance across databases
            if len(results) > 1:
                diagnostics.compare_performance(results)
            
            # Export results for the first query as an example
            if i == 1:
                for db_name, result in results.items():
                    filename = f"diagnostics_{db_name}_query_{i}.json"
                    diagnostics.export_diagnostics_json(result, filename)
        
        # Example: Get database information
        print(f"\n{'DATABASE INFORMATION':-^100}")
        for db_name in diagnostics.databases.keys():
            info = diagnostics.get_database_info(db_name)
            if info:
                print(f"\n{db_name.upper()}:")
                print(f"  MySQL Version: {info['version']}")
                print(f"  Database Size: {info['size_mb']} MB")
                print(f"  Table Count: {info['table_count']}")
    
    finally:
        # Clean up connections
        diagnostics.disconnect_all()
        print("\nAll database connections closed.")

if __name__ == "__main__":
    main()
