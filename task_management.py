from enum import Enum
from datetime import datetime
from threading import Lock

class TaskStatus(Enum):
    PENDING = "Pending"
    IN_PROGRESS = "In Progress"
    COMPLETED = "Completed"

class User:
    def __init__(self, id, name, email):
        self.id = id
        self.name = name
        self.email = email

    def __repr__(self):
        return f"User({self.id}, {self.name})"
    
class Task:
    task_counter = 0

    def __init__(self, title, description, due_date, priority, status=TaskStatus.PENDING, assigned_user=None):
        self.title = title
        self.description = description
        self.due_date = due_date
        self.priority = priority
        self.status = status
        self.assigned_user = assigned_user
        Task.task_counter += 1
        self.task_id = Task.task_counter
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    def update_status(self, status):
        self.status = status
        self.updated_at = datetime.now()

class TaskManager:
    _instance = None
    _lock = Lock()

    def __init__(self):
        if TaskManager._instance is not None:
            raise Exception("This is a singleton class. Use get_instance() method.")
        self.tasks = []
        self._task_lock = Lock()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def create_task(self, title, description, due_date, priority, assigned_user=None):
        with self._task_lock:
            task = Task(title, description, due_date, priority, assigned_user=assigned_user)
            self.tasks.append(task)
            return task
        
    def update_task(self, task_id, title=None, description=None, due_date=None, priority=None, status=None, assigned_user=None):
        if self._task_lock:
            task = self.get_task_by_id(task_id)
            if task:
                task.title = title or task.title
                task.description = description or task.description
                task.due_date = due_date or task.due_date
                task.priority = priority or task.priority
                task.status = status or task.status
                task.assigned_user = assigned_user or task.assigned_user
                task.updated_at = datetime.now()
                return task
        return None
    
    def delete_task(self, task_id):
        with self._task_lock:
            task = self.get_task_by_id(task_id)
            if task:
                self.tasks.remove(task)
                return True
        return False
    
    def search_tasks(self, user=None, priority=None, status=None):
        with self._task_lock:
            filtered_tasks = self.tasks
            if user:
                filtered_tasks = [task for task in filtered_tasks if task.assigned_user==user]
            if priority:
                filtered_tasks = [task for task in filtered_tasks if task.priority==priority]
            if status:
                filtered_tasks = [task for task in filtered_tasks if task.status==status]

            return filtered_tasks
        
    def mark_task_completed(self, task_id):
        with self._task_lock:
            task = self.get_task_by_id(task_id)
            if task:
                task.update_status(TaskStatus.COMPLETED)
                return task
        return None
    
    def get_task_by_id(self, task_id):
        for task in self.tasks:
            if task.task_id == task_id:
                return task
        return None
    
    def get_task_history(self, user):
        with self._task_lock:
            return [task for task in self.tasks if task.assigned_user==user and task.status==TaskStatus.COMPLETED]


def task_management_system_demo():
    # Create the TaskManager instance (Singleton)
    manager = TaskManager.get_instance()

    # Create some users
    user1 = User(1, "Ashutosh", "<EMAIL>")
    user2 = User(2, "John Doe", "<EMAIL>")

    # Create tasks
    task1 = manager.create_task("Task 1", "Description for task 1", datetime(2024, 9, 15), "High", assigned_user=user1)
    task2 = manager.create_task("Task 2", "Description for task 2", datetime(2024, 9, 20), "Medium", assigned_user=user2)

    print("\nTasks created:")
    print(task1)
    print(task2)

    # Update task
    updated_task = manager.update_task(task1.task_id, title="Updated Task 1")
    print("\nUpdated Task 1:")
    print(updated_task)

    # Search for tasks assigned to user1
    print("\nTasks assigned to user1:")
    tasks_for_user1 = manager.search_tasks(user=user1)
    for task in tasks_for_user1:
        print(task)

    # Mark task as completed
    completed_task = manager.mark_task_completed(task1.task_id)
    print("\nTask marked as completed:")
    print(completed_task)

    # View task history for user1
    print("\nTask history for user1:")
    task_history = manager.get_task_history(user1)
    for task in task_history:
        print(task)

# Run the demo
if __name__ == "__main__":
    task_management_system_demo()
