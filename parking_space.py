import datetime


class Car:
    def __init__(self, car_type, start_time):
        self.car_type = car_type
        self.start_time = start_time
        self.parking_space = None
        self.end_time  = None

class ParkingManager:
    def __init__(self, suv_spaces, hatchback_spaces):
        self.suv_spaces = suv_spaces
        self.hatchback_spaces = hatchback_spaces
        self.parked_cars = {}

    def park_cars(self, car_type, start_time):
        car = Car(car_type, start_time)
        if car.car_type == "SUV":
            if self.suv_spaces > 0:
                self.suv_spaces -= 1
                car.parking_space = "SUV"
                self.parked_cars[len(self.parked_cars)] = car
                print("Car parked in SUV space.")
            else:
                print("No space left in SUV parking lot.")
        elif car.car_type == "HATCHBACK":
            if self.hatchback_spaces > 0:
                self.hatchback_spaces -= 1
                car.parking_space = "HATCHBACK"
                self.parked_cars[len(self.parked_cars)] = car
                print("Car parked in HATCHBACK space.")
            else:
                if self.suv_spaces > 0:
                    self.suv_spaces -= 1
                    car.parking_space = "SUV"
                    self.parked_cars[len(self.parked_cars)] = car
                    print("No space left in HATCHBACK parking lot. Car parked in SUV space.")
                else:
                    print("No space left in SUV and HATCHBACK parking lot. Car can't be parked")


    def exit_car(self, car_id, end_time):
        if car_id in self.parked_cars:
            start_time = self.parked_cars[car_id].start_time
            total_fee = self.calculate_fee(end_time, start_time, self.parked_cars[car_id].car_type)
            if self.parked_cars[car_id].car_type == "SUV":
                self.suv_spaces += 1
            elif self.parked_cars[car_id].car_type == "HATCHBACK" and self.parked_cars[car_id].parking_space == "HATCHBACK":
                self.hatchback_spaces += 1
            elif self.parked_cars[car_id].car_type == "HATCHBACK" and self.parked_cars[car_id].parking_space == "SUV":
                self.suv_spaces += 1
            del self.parked_cars[car_id]
            print(f"Total fee for {car_id} is {total_fee}")
        else:
            print(f"{car_id} not in parking lot.")

    def calculate_fee(self, end_time, start_time, car_type):
        if car_type == "SUV":
            total_fee = (end_time - start_time) * 20
        elif car_type == "HATCHBACK":
            total_fee = (end_time - start_time) * 10
        return total_fee

    def view_parked_cars(self, user):
        if user != "admin":
            return "Only admin have permission to view parked spaces."
        if not self.parked_cars:
            return f"Parking lot is empty."
        else:
            print(f"Available SUV space - {self.suv_spaces} , Available HATCHBACK spaces - {self.hatchback_spaces}")
            for key, val in self.parked_cars.items(): 
                print(f"\nCar id - {key}, Car Type - {val.car_type}, Start Time - {val.start_time}, Parking Space - {val.parking_space}\n")
            

if __name__=="__main__":
    parking_manager = ParkingManager(5, 3)

    parking_manager.park_cars("SUV", 2)
    parking_manager.park_cars("SUV", 2)
    parking_manager.park_cars("HATCHBACK", 2)
    parking_manager.park_cars("HATCHBACK", 2)
    parking_manager.park_cars("HATCHBACK", 2)
    parking_manager.park_cars("HATCHBACK", 2)
    parking_manager.exit_car(4, 5)
    parking_manager.exit_car(5, 5)
    parking_manager.park_cars("SUV", 6)
    parking_manager.park_cars("SUV", 7)


    parking_manager.view_parked_cars("admin")
    