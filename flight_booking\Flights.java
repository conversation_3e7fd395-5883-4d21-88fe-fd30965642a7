package flight_booking;

public class Flights {
    private final String flightNumber;
    private final String source;
    private final String destination;
    private final String date;
    private int availableSeats;

    public Flights(String flightNumber, String source, String destination,
    String date, int availableSeats){
        this.flightNumber = flightNumber;
        this.source = source;
        this.destination = destination;
        this.date = date;
        this.availableSeats = availableSeats;
    }
    
    public String getFlightNumber() { return flightNumber; }
    public String getSource() { return source; }
    public String getDestination() { return destination; }
    public String getDate() { return date; }
    public int getAvailableSeats() { return availableSeats; }

    public void bookSeat(){
        if (availableSeats <= 0){
            throw new IllegalStateException("No available seats in flight: " + flightNumber);
        }
        availableSeats -= 1;
    }

    public void cancelSeat(){
        availableSeats += 1;
    }

}
