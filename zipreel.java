/**
* ZipReel Movie Management System (Java Implementation)
*
* Features:
* - Movie and User registration
* - Multi-level caching: L1 (per-user LRU), L2 (global LFU), Primary Store (in-memory)
* - Search operations: TITLE, GENRE, YEAR, MULTI-filter
* - Cache stats and clearing caches
* - Command-line interface (reads from stdin lines)
*
* Commands supported (examples):
* ADD_MOVIE 1 "Inception" "Sci-Fi" 2010 9.5
* ADD_USER 1 "John" "Action"
* SEARCH 1 TITLE "Inception"
* SEARCH 1 GENRE "Action"
* SEARCH_MULTI 1 "Action" 2020 8.0
* VIEW_CACHE_STATS
* CLEAR_CACHE L1
*
* Note: Single-file demo implementation suitable for interview / local testing.
*/

import java.util.*;
import java.io.*;

public class zipReel{
    static final class Movie{
        private final int id;
        private final String title;
        private final String genre;
        private final int year;
        private final double rating;

        Movie(int id, String title, String genre, int year, double rating){
            this.id = id;
            this.title = title;
            this.genre = genre;
            this.year = year;
            this.rating = rating;
        }
        public int getId() { return id; }
        public String getTitle() { return title; }
        public String genre() { return genre; }
        public int year() { return year; }
        public Double rating() { return rating; }

        public String display(){
            return String.format("%s (%d) - %s - %.1f", title, year, genre, year, rating);
        }
    }
    static final class User{
        private final int id;
        private final String name;
        private final String preferredGenre;

        User(int id, String name, String preferredGenre){
            this.id = id;
            this.name = name;
            this.preferredGenre = preferredGenre;
        }

        public int getId() { return id; }
        public String getName() { return name; }
        public String getPreferredGenre() { return preferredGenre; }
    }

    static final class PrimaryStore{
        private final Map<Integer, Movie> movieMap = new ConcurrentHashMap<>();

        public void addMovie(Movie m){
            if (movieMap.contiansKey(m.getId())){
                throw new IllegalArgumentException("Movie with ID " + m.getId() + " already exists.");
            }
            movieMap.put(m.getId(), m);
        }
        public Movie getMovie(int id) { return movieMap.get(id); }

        public List<Movie> searchByTitle(String title) {
            final String t = title.toLowerCase();

            return movieMap.values().stream().filter(m -> m.getTitle().toLowerCase().contains(t)).collect(Collectors.toList());
        }

        public List<Movie> searchByGenre(String genre) {
            final String g = genre.toLowerCase();

            return movieMap.values().stream().filter(m -> m.getGenre().toLowerCase().contains(t)).collect(Collectors.toList());
        }

        public List<Movie> searchByYear(String year) {
            final int y = year.toLowerCase();

            return movieMap.values().stream().filter(m -> m.getYear().toLowerCase().contains(t)).collect(Collectors.toList());
        }
    }

    static final class L1Cache {
        private final int capacity;
        private final Map<K, Node> map;
        private final DoublyLinkedList dll;

        private class Node{
            K key;
            V value;
            Node prev;
            Node next;
            Node(K key, V value){
                this.key = key;
                this.value = value;
            }
        }
        private class DoublyLinkedList{
            Node head;
            Node tail;

            DoublyLinkedList(){
                head = new Node(null, null);
                tail = new Node(null, null);
                head.next = tail;
                tail.prev = head;
            }

            void addFirst(Node node){
                node.next = head.next;
                node.prev = head;
                head.next.prev = node;
                head.next = node;
            }

            void remove(Node node){
                node.prev.next = node.next
                node.next.prev = node.prev;
            }

            void removeLast(){
                if (tail.prev == head) { return null; }
                Node last = tail.prev;
                remove(last);
                return last;
            }
        }
        
        public L1Cache(int capacity){
            this.capacity = capacity;
            this.map = new HashMap<>();
            this.dll = new DoublyLinkedList();
        }

        public synchronized List<zipReel.Movie> get(String key){
            Node node = map.get(key);
            if (node == null) return null;
            dll.remove()
            dll.addFirst(node);
            return node.value;
        }

        public synchronized put(String key, List<zipReel.Movie> value){
            if (map.contiansKey(key)){
                Node node = map.get(key);
                node.value = value;
                dll.remove(node);
                dll.addFirst(node);
            }
            else{
                Node node = new Node(key, value);
                dll.addFirst(node);
                map.put(key, node);
                if (map.size() > capacity){
                    Node lru = dll.removeLast();
                    if (lru != null) map.remove(lru.key);
                }
            }
        }

        
    }
}