#!/usr/bin/env python3
"""
Cross-Database MySQL Query Diagnostics Tool
Runs EXPLAIN plans and provides query performance analysis across multiple MySQL databases.
"""

import mysql.connector
from mysql.connector import Error
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database connection configuration"""
    host: str
    port: int = 3306
    database: str = ""
    user: str = ""
    password: str = ""
    name: str = ""  # Friendly name for the database

@dataclass
class ExplainResult:
    """EXPLAIN plan result structure"""
    id: int
    select_type: str
    table: str
    partitions: Optional[str]
    type: str
    possible_keys: Optional[str]
    key: Optional[str]
    key_len: Optional[str]
    ref: Optional[str]
    rows: int
    filtered: float
    extra: Optional[str]

@dataclass
class QueryDiagnostics:
    """Complete query diagnostics result"""
    database_name: str
    query: str
    execution_time: float
    explain_plan: List[ExplainResult]
    warnings: List[str]
    recommendations: List[str]
    timestamp: str

class MySQLQueryDiagnostics:
    """Cross-database MySQL query diagnostics tool"""
    
    def __init__(self):
        self.databases: Dict[str, DatabaseConfig] = {}
        self.connections: Dict[str, mysql.connector.MySQLConnection] = {}
    
    def add_database(self, config: DatabaseConfig) -> None:
        """Add a database configuration"""
        self.databases[config.name] = config
        logger.info(f"Added database configuration: {config.name}")
    
    def connect_to_database(self, db_name: str) -> bool:
        """Establish connection to a specific database"""
        if db_name not in self.databases:
            logger.error(f"Database {db_name} not configured")
            return False
        
        config = self.databases[db_name]
        try:
            connection = mysql.connector.connect(
                host=config.host,
                port=config.port,
                database=config.database,
                user=config.user,
                password=config.password,
                autocommit=True
            )
            
            if connection.is_connected():
                self.connections[db_name] = connection
                logger.info(f"Connected to database: {db_name}")
                return True
                
        except Error as e:
            logger.error(f"Error connecting to {db_name}: {e}")
            return False
    
    def disconnect_database(self, db_name: str) -> None:
        """Disconnect from a specific database"""
        if db_name in self.connections:
            self.connections[db_name].close()
            del self.connections[db_name]
            logger.info(f"Disconnected from database: {db_name}")
    
    def disconnect_all(self) -> None:
        """Disconnect from all databases"""
        for db_name in list(self.connections.keys()):
            self.disconnect_database(db_name)
    
    def run_explain(self, db_name: str, query: str) -> Optional[List[ExplainResult]]:
        """Run EXPLAIN on a query and return structured results"""
        if db_name not in self.connections:
            if not self.connect_to_database(db_name):
                return None
        
        connection = self.connections[db_name]
        cursor = connection.cursor(dictionary=True)
        
        try:
            # Run EXPLAIN
            explain_query = f"EXPLAIN {query}"
            cursor.execute(explain_query)
            results = cursor.fetchall()
            
            explain_results = []
            for row in results:
                explain_result = ExplainResult(
                    id=row.get('id', 0),
                    select_type=row.get('select_type', ''),
                    table=row.get('table', ''),
                    partitions=row.get('partitions'),
                    type=row.get('type', ''),
                    possible_keys=row.get('possible_keys'),
                    key=row.get('key'),
                    key_len=row.get('key_len'),
                    ref=row.get('ref'),
                    rows=row.get('rows', 0),
                    filtered=row.get('filtered', 0.0),
                    extra=row.get('Extra')
                )
                explain_results.append(explain_result)
            
            return explain_results
            
        except Error as e:
            logger.error(f"Error running EXPLAIN on {db_name}: {e}")
            return None
        finally:
            cursor.close()

    def analyze_explain_plan(self, explain_results: List[ExplainResult]) -> Tuple[List[str], List[str]]:
        """Analyze EXPLAIN plan and generate warnings and recommendations"""
        warnings = []
        recommendations = []

        for result in explain_results:
            # Check for table scans
            if result.type == 'ALL':
                warnings.append(f"Full table scan on table '{result.table}' (rows: {result.rows})")
                recommendations.append(f"Consider adding an index on table '{result.table}'")

            # Check for high row counts
            if result.rows > 10000:
                warnings.append(f"High row count ({result.rows}) for table '{result.table}'")
                recommendations.append(f"Consider optimizing query or adding WHERE clause for table '{result.table}'")

            # Check for filesort
            if result.extra and 'Using filesort' in result.extra:
                warnings.append(f"Filesort operation detected on table '{result.table}'")
                recommendations.append(f"Consider adding an index to avoid filesort on table '{result.table}'")

            # Check for temporary tables
            if result.extra and 'Using temporary' in result.extra:
                warnings.append(f"Temporary table created for table '{result.table}'")
                recommendations.append(f"Consider optimizing GROUP BY or ORDER BY clauses")

            # Check for missing indexes
            if not result.key and result.type not in ['system', 'const']:
                warnings.append(f"No index used for table '{result.table}'")
                recommendations.append(f"Consider adding appropriate indexes for table '{result.table}'")

            # Check for low filtered percentage
            if result.filtered < 10.0 and result.rows > 100:
                warnings.append(f"Low selectivity ({result.filtered}%) on table '{result.table}'")
                recommendations.append(f"Consider adding more selective WHERE conditions")

        return warnings, recommendations

    def execute_with_timing(self, db_name: str, query: str) -> Optional[float]:
        """Execute query and measure execution time"""
        if db_name not in self.connections:
            if not self.connect_to_database(db_name):
                return None

        connection = self.connections[db_name]
        cursor = connection.cursor()

        try:
            start_time = time.time()
            cursor.execute(query)
            cursor.fetchall()  # Fetch all results to ensure complete execution
            end_time = time.time()

            return end_time - start_time

        except Error as e:
            logger.error(f"Error executing query on {db_name}: {e}")
            return None
        finally:
            cursor.close()

    def diagnose_query(self, db_name: str, query: str) -> Optional[QueryDiagnostics]:
        """Complete query diagnostics including EXPLAIN plan and performance analysis"""
        # Run EXPLAIN
        explain_results = self.run_explain(db_name, query)
        if not explain_results:
            return None

        # Measure execution time
        execution_time = self.execute_with_timing(db_name, query)
        if execution_time is None:
            execution_time = 0.0

        # Analyze EXPLAIN plan
        warnings, recommendations = self.analyze_explain_plan(explain_results)

        # Create diagnostics result
        diagnostics = QueryDiagnostics(
            database_name=db_name,
            query=query.strip(),
            execution_time=execution_time,
            explain_plan=explain_results,
            warnings=warnings,
            recommendations=recommendations,
            timestamp=datetime.now().isoformat()
        )

        return diagnostics

    def diagnose_across_databases(self, query: str, db_names: Optional[List[str]] = None) -> Dict[str, QueryDiagnostics]:
        """Run diagnostics across multiple databases"""
        if db_names is None:
            db_names = list(self.databases.keys())

        results = {}
        for db_name in db_names:
            logger.info(f"Running diagnostics on database: {db_name}")
            diagnostics = self.diagnose_query(db_name, query)
            if diagnostics:
                results[db_name] = diagnostics

        return results

    def print_diagnostics_report(self, diagnostics: QueryDiagnostics) -> None:
        """Print a formatted diagnostics report"""
        print(f"\n{'='*80}")
        print(f"QUERY DIAGNOSTICS REPORT - {diagnostics.database_name}")
        print(f"{'='*80}")
        print(f"Timestamp: {diagnostics.timestamp}")
        print(f"Execution Time: {diagnostics.execution_time:.4f} seconds")
        print(f"\nQuery:")
        print(f"{diagnostics.query}")

        print(f"\n{'EXPLAIN PLAN':-^80}")
        print(f"{'ID':<3} {'Type':<12} {'Table':<15} {'Key':<15} {'Rows':<8} {'Extra':<20}")
        print(f"{'-'*80}")

        for result in diagnostics.explain_plan:
            extra = (result.extra or '')[:20] + '...' if result.extra and len(result.extra) > 20 else (result.extra or '')
            print(f"{result.id:<3} {result.type:<12} {result.table:<15} {(result.key or 'None'):<15} {result.rows:<8} {extra:<20}")

        if diagnostics.warnings:
            print(f"\n{'WARNINGS':-^80}")
            for i, warning in enumerate(diagnostics.warnings, 1):
                print(f"{i}. {warning}")

        if diagnostics.recommendations:
            print(f"\n{'RECOMMENDATIONS':-^80}")
            for i, rec in enumerate(diagnostics.recommendations, 1):
                print(f"{i}. {rec}")

        print(f"{'='*80}\n")

    def export_diagnostics_json(self, diagnostics: QueryDiagnostics, filename: str) -> None:
        """Export diagnostics to JSON file"""
        try:
            # Convert dataclass to dict for JSON serialization
            data = asdict(diagnostics)

            with open(filename, 'w') as f:
                json.dump(data, f, indent=2, default=str)

            logger.info(f"Diagnostics exported to {filename}")

        except Exception as e:
            logger.error(f"Error exporting diagnostics: {e}")

    def compare_performance(self, results: Dict[str, QueryDiagnostics]) -> None:
        """Compare query performance across databases"""
        if len(results) < 2:
            print("Need at least 2 databases to compare performance")
            return

        print(f"\n{'PERFORMANCE COMPARISON':-^80}")
        print(f"{'Database':<20} {'Execution Time':<15} {'Total Rows':<12} {'Warnings':<10}")
        print(f"{'-'*80}")

        sorted_results = sorted(results.items(), key=lambda x: x[1].execution_time)

        for db_name, diagnostics in sorted_results:
            total_rows = sum(result.rows for result in diagnostics.explain_plan)
            warning_count = len(diagnostics.warnings)

            print(f"{db_name:<20} {diagnostics.execution_time:<15.4f} {total_rows:<12} {warning_count:<10}")

        # Highlight best and worst performers
        best_db = sorted_results[0][0]
        worst_db = sorted_results[-1][0]

        print(f"\nBest Performance: {best_db} ({sorted_results[0][1].execution_time:.4f}s)")
        print(f"Worst Performance: {worst_db} ({sorted_results[-1][1].execution_time:.4f}s)")

    def get_database_info(self, db_name: str) -> Optional[Dict[str, Any]]:
        """Get database server information"""
        if db_name not in self.connections:
            if not self.connect_to_database(db_name):
                return None

        connection = self.connections[db_name]
        cursor = connection.cursor(dictionary=True)

        try:
            info = {}

            # Get MySQL version
            cursor.execute("SELECT VERSION() as version")
            info['version'] = cursor.fetchone()['version']

            # Get database size
            cursor.execute(f"SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='{self.databases[db_name].database}'")
            result = cursor.fetchone()
            info['size_mb'] = result['DB Size in MB'] if result['DB Size in MB'] else 0

            # Get table count
            cursor.execute(f"SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='{self.databases[db_name].database}'")
            info['table_count'] = cursor.fetchone()['table_count']

            return info

        except Error as e:
            logger.error(f"Error getting database info for {db_name}: {e}")
            return None
        finally:
            cursor.close()
