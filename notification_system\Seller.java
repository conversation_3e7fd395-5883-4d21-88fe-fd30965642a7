package notification_system;

public class Seller extends User {
    private int sellerId;

    public Seller(int userId, String userName, String email, String phoneNumber, int sellerId) {
        super(userId, userName, email, phoneNumber);
        this.sellerId = sellerId;
    }

    public int getSellerId() {
        return sellerId;
    }

    //seller will only get notified when an order is placed.
    public String notifySeller(int userId, int orderId, Event event, ChannelType channelType) {
        if (event.getEventType() == EventType.PLACED) {
            return "Seller " + userId + " notified for order " + orderId;
        }
        return "Seller " + userId + " not notified for order " + orderId;
    }
}
