print("Testing BNPL System...")

try:
    print("1. Testing basic Python...")
    print("   ✅ Python is working")
    
    print("2. Testing imports...")
    import sys
    import os
    sys.path.insert(0, os.getcwd())
    
    from bnpl_system.PaymentMethods import PaymentMethod
    print("   ✅ PaymentMethod imported")
    
    from bnpl_system.models.User import User
    print("   ✅ User model imported")
    
    from bnpl_system.models.Item import Item
    print("   ✅ Item model imported")
    
    from bnpl_system.service.UserService import UserService
    print("   ✅ UserService imported")
    
    print("3. Testing basic functionality...")
    user_service = UserService()
    user = user_service.register_user("test", 1000.0)
    print(f"   ✅ User created: {user}")
    
    item = Item("TestItem", 10.0, 5)
    print(f"   ✅ Item created: {item}")
    
    print("\n🎉 SUCCESS! BNPL system is working!")
    
except Exception as e:
    print(f"\n❌ ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\nTest completed.")
