package cab_booking;

import java.util.HashMap;
import java.util.Map;

public class Main {
    public static void main(String[] args) {
        System.out.println("Hello, Cab Booking System!");

        Map<String, Driver> driverMap = new HashMap<>();
        Map<String, Rider> riderMap = new HashMap<>();
        Map<String, Ride> rideMap = new HashMap<>();

        // Initialize services with shared state
        DriverService driverService = new DriverService(driverMap);
        RiderService riderService = new RiderService(riderMap, driverService, rideMap);

        // Create sample data
        Rider rider = new Rider("Alice", "R1", new Location(0, 0));
        Driver driver1 = new Driver("Bob", "D1", new Location(1, 1));
        Driver driver2 = new Driver("Charlie", "D2", new Location(2, 2));

        // Register drivers
        System.out.println(driverService.addDriver(driver1.getName(), driver1.getDriverId(), driver1.getLocation()));
        System.out.println(driverService.addDriver(driver2.getName(), driver2.getDriverId(), driver2.getLocation()));

        // Register rider
        System.out.println(riderService.addRider(rider.getName(), rider.getRiderId(), rider.getLocation()));

        // Request ride
        String rideInfo = riderService.requestRide(rider.getRiderId(), new Location(3, 3));
        System.out.println(rideInfo);

        // End ride (simulate)
        String rideId = rideInfo.substring(rideInfo.indexOf("Ride ID: ") + 9, rideInfo.length() - 1); // crude extraction
        System.out.println(riderService.endTrip(rideId, new Location(5, 5)));

        //give more complex test cases here by adding more drivers and riders and requesting rides
        Rider rider2 = new Rider("Bob", "R2", new Location(0, 0));
        System.out.println(riderService.addRider(rider2.getName(), rider2.getRiderId(), rider2.getLocation()));
        Rider rider3 = new Rider("Charlie", "R3", new Location(0, 0));
        System.out.println(riderService.addRider(rider3.getName(), rider3.getRiderId(), rider3.getLocation()));
        Rider rider4 = new Rider("David", "R4", new Location(0, 0));
        System.out.println(riderService.addRider(rider4.getName(), rider4.getRiderId(), rider4.getLocation()));
        System.out.println(riderService.requestRide(rider2.getRiderId(), new Location
(3, 3)));
        System.out.println(riderService.requestRide(rider3.getRiderId(), new Location
(3, 3)));
        System.out.println(riderService.requestRide(rider4.getRiderId(), new Location
(3, 3)));   
    }
}
