#!/usr/bin/env python3

import sys
import os
sys.path.append(os.getcwd())

print("Testing basic BNPL functionality...")

try:
    # Test 1: Import UserService
    print("1. Testing UserService import...")
    from BNPL.service.UserService import UserService
    print("   ✅ UserService imported successfully")
    
    # Test 2: Create UserService instance
    print("2. Testing UserService instantiation...")
    user_service = UserService()
    print("   ✅ UserService created successfully")
    
    # Test 3: Register a user
    print("3. Testing user registration...")
    user = user_service.register_user("test_user", 1000.0)
    print(f"   ✅ User registered: {user}")
    
    # Test 4: Retrieve user
    print("4. Testing user retrieval...")
    retrieved_user = user_service.get_user("test_user")
    print(f"   ✅ User retrieved: {retrieved_user}")
    
    # Test 5: Test PaymentMethod enum
    print("5. Testing PaymentMethod enum...")
    from BNPL.PaymentMethods import PaymentMethod
    print(f"   ✅ PaymentMethod.BNPL = {PaymentMethod.BNPL}")
    print(f"   ✅ PaymentMethod.PREPAID = {PaymentMethod.PREPAID}")
    
    # Test 6: Test models
    print("6. Testing models...")
    from BNPL.models.User import User
    from BNPL.models.Item import Item
    from BNPL.models.Order import Order
    
    test_user = User("test", 500.0)
    test_item = Item("TestItem", 10.0, 5)
    print(f"   ✅ Models work: {test_user}, {test_item}")
    
    print("\n🎉 All basic tests passed!")
    print("The BNPL system core components are working correctly.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
