from typing import List

class User:
    def __init__(self, user_id: str, credit_limit: float, orders: List = None):
        self.user_id = user_id
        self.credit_limit = credit_limit
        self.remaining_limit = credit_limit
        # default factory pattern to ensure each user gets their own list
        self.orders = orders if orders is not None else []
    
    def __str__(self):
        return f"User(id={self.user_id}, credit_limit={self.credit_limit}, remaining_limit={self.remaining_limit})"
