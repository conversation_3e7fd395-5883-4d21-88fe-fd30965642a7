class CoffeeMaker:
    def __init__(self):
        self.admin = "admin"
        self.ingredients = {
            "coffee_beans": 5,
            "milk": 5,
            "water": 5 
        }
        self.recipies = {
            "Espresso": {"coffee_beans": 3, "water": 1},
            "Americano": {"coffee_beans": 2, "water": 3},
            "Latte": {"coffee_beans": 2, "water": 2, "milk": 2},
        }

        self.menu = list(self.recipies.keys())

    def display_menu(self):
        print(f"Menu: {(', '.join(self.menu))}")

    def customize_recipe(self, drink, custom_recipe):
        if not drink in self.recipies:
            print("Invalid drink. Please choose a drink from menu")
        
        else:
            self.recipies[drink] = custom_recipe
            print(f"{drink} customized!")


    def see_ingredients(self, recipe):
        for ingredient, amount in recipe.items():
            if self.ingredients[ingredient] < amount:
                return False
            
        return True
    
    def prepare_drink(self, drink):
        if not drink in self.recipies:
            print("Invalid drink. Please choose a drink from menu")
        
        else:
            recipe = self.recipies[drink]
            if self.see_ingredients(recipe):
                for ingredient, amount in recipe.items():
                    self.ingredients[ingredient] -= amount
                print(f"{drink} is ready!")
            else:
                print("Not enough ingredients.")
    
    def add_drink(self, drink, recipe):
        if drink in self.recipies:
            print(f"{drink} already present in menu.")
        
        else:
            self.recipe[drink] = recipe
            self.menu.append(drink)
            print(f"{drink} added to menu!")
    
    def add_ingredients(self, user, ingredients):
        if user != self.admin:
            print("Only admin can add ingredients.")
        else:
            for ingredient, amount in ingredients.items():
                if ingredient in self.ingredients:
                    self.ingredients[ingredient] += amount
                else:
                    print(f"{ingredient} is not a valid ingredient. Please choose from the following ingredients: {(' '.join(self.ingredients.keys()))}")
            
            print("Ingredients added successfully.")


if __name__=="__main__":

    coffee_maker = CoffeeMaker()

    while True:
        print("\nChoose an Option:")
        print("1. Display menu.")
        print("2. Get a drink")
        print("3. Customize a drink")
        print("4. Add ingredients.")
        print("5. Exit program\n")

        choice = int(input("Enter choice from 1-5: "))

        if choice == 1:
            coffee_maker.display_menu()
        elif choice == 2:
            item = input("Enter drink you want to order: ")
            coffee_maker.prepare_drink(item)
        elif choice == 3:
            item = input("Enter drink you want to customize: ")
            custom_recipe = {}
            for ing in coffee_maker.ingredients:
                amount = int(input(f"Enter amount of {ing}: "))
                custom_recipe[ing] = amount
            coffee_maker.customize_recipe(item, custom_recipe)
        elif choice == 4:
            user = input("Enter user name: ")
            ingredients = {}
            for ing in coffee_maker.ingredients:
                amount = int(input(f"Enter amount of {ing}: "))
                ingredients[ing] = amount
            coffee_maker.add_ingredients(user, ingredients)
        elif choice == 5:
            break
        else:
            print("Wrong choice. Please choose from 1-5")