from typing import List

class User:
    def __init__(self, user_id: str, credit_limit: float = 0, remaining_limit: float = None):
        self.user_id = user_id
        self.credit_limit = credit_limit
        self.remaining_limit = remaining_limit if remaining_limit is not None else credit_limit
        self.orders = []  # List of Order objects
        self.blacklisted = False # will be implemented in future

    def __str__(self):
        return f"User(user_id={self.user_id}, credit_limit={self.credit_limit}, remaining_limit={self.remaining_limit})"