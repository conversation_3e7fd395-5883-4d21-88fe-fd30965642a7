import json
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
import requests
import redis
import time
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()
r = redis.StrictRedis(host="localhost", port=6379, db=0, decode_responses=True)
DEFAULT_EXPIRATION = 3600

class TimingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Request to {request.url} took {duration:.4f} seconds")
        return response

# Add the middleware to your FastAPI app
app.add_middleware(TimingMiddleware)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Route for getting photos by albumId
@app.get("/photos")
def get_photos(albumId: int = Query(None)):
    cache_key = f"photos:{albumId}"
    cached_data = r.get(cache_key)
    if cached_data:
        logger.info(f"Cache hit for {albumId}")
        return json.loads(cached_data)
    
    response = requests.get(
        "https://jsonplaceholder.typicode.com/photos",
        params={"albumId": albumId}
    )
    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail="Error fetching data")
    response_data = json.dumps(response.json())
    r.setex("photos", DEFAULT_EXPIRATION, response_data)
    return response_data

# Route for getting a specific photo by id
@app.get("/photos/{id}")
def get_photo(id: int):
    response = requests.get(f"https://jsonplaceholder.typicode.com/photos/{id}")
    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail="Error fetching data")
    return response.json()

# Run the app using: uvicorn filename:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
