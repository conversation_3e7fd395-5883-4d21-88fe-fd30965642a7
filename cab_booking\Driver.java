package cab_booking;

public class Driver {
    private String name;
    private String driverId;
    private Location location;
    private boolean isAvailable;

    public Driver(String name, String driverId, Location location){
        this.name = name;
        this.driverId = driverId;
        this.location = location;
        this.isAvailable = true;
    }

    public String getName() { return name; }
    public String getDriverId() { return driverId; }
    public Location getLocation() { return location; }
    public boolean getIsAvailable() { return isAvailable; }
    public void setIsAvailable(boolean isAvailable) { this.isAvailable = isAvailable; }

}
