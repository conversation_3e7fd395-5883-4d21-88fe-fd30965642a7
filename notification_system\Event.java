package notification_system;

public class Event {
    private EventType eventType;
    private int orderId;
    private int customerId;
    private int sellerId;
    private int deliveryAgentId;
    private String notificationMessage;

    public Event(EventType eventType, int orderId, int customerId, int sellerId, int deliveryAgentId, String notificationMessage){
        this.eventType = eventType;
        this.orderId = orderId;
        this.customerId = customerId;
        this.sellerId = sellerId;
        this.deliveryAgentId = deliveryAgentId;
        this.notificationMessage = "";
    }
    public EventType getEventType() {
        return eventType;
    }
    public int getOrderId() {
        return orderId;
    }
    public int getCustomerId() {
        return customerId;
    }
    public int getSellerId() {
        return sellerId;
    }
    public int getDeliveryAgentId() {
        return deliveryAgentId;
    }
    public String getNotificationMessage() {
        return notificationMessage;
    }
}
