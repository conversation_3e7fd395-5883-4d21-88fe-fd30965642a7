# FWCRCEC
# Use the official Python image from Docker Hub
FROM python:3.9-slim

# Set the working directory
WORKDIR /app

# Copy the requirements file into the container
COPY requirements.txt .

# Install the dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code into the container
COPY . .

# Expose the port on which the FastAPI app will run
EXPOSE 5000

# Command to run the FastAPI application using uvicorn
CMD ["uvicorn", "lru:app", "--host", "0.0.0.0", "--port", "5000"]

# Sample build command
# docker build -t ashutoshchatterjee98/lruapp:2.4 . 
# -t is for naming, ashutoshchatterjee98 is the user name, lruapp is the container name, 2.4 is version

# Sample run command
# docker run -p 5000:5000 ashutoshchatterjee98/lruapp:2.3 
# -p is used to map local to container host.
