#!/usr/bin/env python3
"""
Simple test setup for MySQL diagnostics tool
"""

import mysql.connector
from mysql.connector import Error
import j<PERSON>

def test_mysql_connection():
    """Test MySQL connection with common default settings"""
    
    # Common MySQL default settings
    configs_to_try = [
        {"host": "localhost", "port": 3306, "user": "root", "password": ""},
        {"host": "localhost", "port": 3306, "user": "root", "password": "root"},
        {"host": "localhost", "port": 3306, "user": "root", "password": "password"},
        {"host": "127.0.0.1", "port": 3306, "user": "root", "password": ""},
    ]
    
    print("Testing MySQL connection with common default settings...")
    print("=" * 60)
    
    for i, config in enumerate(configs_to_try, 1):
        try:
            print(f"\nTrying configuration {i}: {config['host']}:{config['port']} with user '{config['user']}'")
            
            connection = mysql.connector.connect(
                host=config["host"],
                port=config["port"],
                user=config["user"],
                password=config["password"],
                connect_timeout=5
            )
            
            if connection.is_connected():
                print("✅ Connection successful!")
                
                # Get MySQL version
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                print(f"MySQL Version: {version}")
                
                # Create test database
                print("\nCreating test database...")
                cursor.execute("DROP DATABASE IF EXISTS ecommerce_test")
                cursor.execute("CREATE DATABASE ecommerce_test")
                cursor.execute("USE ecommerce_test")
                
                # Create simple test table
                cursor.execute("""
                    CREATE TABLE test_users (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        email VARCHAR(150) NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_email (email)
                    )
                """)
                
                # Insert test data
                test_data = [
                    ('John Doe', '<EMAIL>'),
                    ('Jane Smith', '<EMAIL>'),
                    ('Bob Johnson', '<EMAIL>'),
                    ('Alice Brown', '<EMAIL>'),
                    ('Charlie Wilson', '<EMAIL>')
                ]
                
                cursor.executemany(
                    "INSERT INTO test_users (name, email) VALUES (%s, %s)",
                    test_data
                )
                
                print("✅ Test database and data created successfully!")
                
                # Create config file
                test_config = {
                    "databases": [
                        {
                            "name": "test_db",
                            "host": config["host"],
                            "port": config["port"],
                            "database": "ecommerce_test",
                            "user": config["user"],
                            "password": config["password"]
                        }
                    ]
                }
                
                with open('mysql_test_config.json', 'w') as f:
                    json.dump(test_config, f, indent=2)
                
                print("✅ Configuration file 'mysql_test_config.json' created!")
                
                cursor.close()
                connection.close()
                
                print(f"\n{'='*60}")
                print("🎉 Setup completed successfully!")
                print(f"{'='*60}")
                print("Database: ecommerce_test")
                print("Table: test_users (5 sample records)")
                print("Config: mysql_test_config.json")
                print("\nNow you can test the diagnostics tool!")
                
                return True
                
        except Error as e:
            print(f"❌ Connection failed: {e}")
            continue
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            continue
    
    print(f"\n{'='*60}")
    print("❌ Could not connect to MySQL with any default configuration.")
    print("Please check:")
    print("1. MySQL server is running")
    print("2. Correct username/password")
    print("3. MySQL is accessible on localhost:3306")
    print(f"{'='*60}")
    return False

if __name__ == "__main__":
    test_mysql_connection()
