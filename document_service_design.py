class Document:
    def __init__(self, name, content, owner):
        self.name = name
        self.content = content
        self.owner = owner
        self.permissions = {owner: {"read": True, "edit": True}}  # Grant owner full access initially

    def grant_access(self, user, permission_type):
        if user == self.owner:
            raise ValueError("Owner already has full access.")
        valid_permissions = ["read", "edit"]
        if permission_type not in valid_permissions:
            raise ValueError(f"Invalid permission type: {permission_type}. Valid types are: {', '.join(valid_permissions)}")
        self.permissions.setdefault(user, {})[permission_type] = True

    def can_access(self, user, permission_type):
        if user == self.owner:
            return True  # Owner always has full access
        return permission_type in self.permissions.get(user, {}) and self.permissions[user][permission_type]

class DocumentService:
    def __init__(self):
        self.documents = {}

    def create_document(self, user, name, content):
        new_document = Document(name, content, user)
        self.documents[name] = new_document
        return new_document

    def read_document(self, user, document_name):
        document = self.documents.get(document_name)
        if not document:
            raise ValueError(f"Document '{document_name}' does not exist.")
        if not document.can_access(user, "read"):
            raise PermissionError(f"User '{user}' does not have read permission for document '{document_name}'.")
        return document.content

    def edit_document(self, user, document_name, new_content):
        document = self.documents.get(document_name)
        if not document:
            raise ValueError(f"Document '{document_name}' does not exist.")
        if not document.can_access(user, "edit"):
            raise PermissionError(f"User '{user}' does not have edit permission for document '{document_name}'.")
        document.content = new_content
        return document  # Return the updated document

    def delete_document(self, user, document_name):
        document = self.documents.get(document_name)
        if not document:
            raise ValueError(f"Document '{document_name}' does not exist.")
        if document.owner != user:
            raise PermissionError(f"User '{user}' is not the owner of document '{document_name}' and cannot delete it.")
        del self.documents[document_name]
        return f"Document '{document_name}' deleted successfully."


if __name__ == "__main__":
  # Create a DocumentService instance
  document_service = DocumentService()

  # Create a user (owner)
  owner = "Alice"

  # Create a document
  document_service.create_document(owner, "My Document", "Initial content")

  # Another user trying to edit (without permission)
  user1 = "Bob"
  try:
    document_service.edit_document(user1, "My Document", "Unauthorized edit")
  except PermissionError as e:
    print(e)  # Output: User 'Bob' does not have edit permission for document 'My Document'

  # Owner granting read access to user1
  document_service.documents["My Document"].grant_access(user1, "read")

  # User1 reading the document
  print(document_service.read_document(user1, "My Document"))  # Output: Initial content

  # User1 trying to edit again (now with read permission)
  try:
    document_service.edit_document(user1, "My Document", "Edited content")
  except PermissionError as e:
    print(e)  # Output: User 'Bob' does not have edit permission for document 'My Document'

  # Owner editing the document
  document_service.edit_document(owner, "My Document", "Updated content by owner")

  # User1 reading the updated document
  print(document_service.read_document(user1, "My Document"))  # Output: Updated content by owner

  # Another user (user2) trying to delete the document
  user2 = "Charlie"
  try:
    document_service.delete_document(user2, "My Document")
  except PermissionError as e:
    print(e)  # Output: User 'Charlie' is not the owner of document 'My Document' and cannot delete it.

  # Owner deleting the document
  print(document_service.delete_document(owner, "My Document"))  # Output: Document 'My Document' deleted successfully.

