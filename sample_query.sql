-- Sample complex query for testing MySQL diagnostics tool
-- This query demonstrates various potential performance issues

SELECT 
    u.id,
    u.name,
    u.email,
    u.created_at,
    COUNT(o.id) as total_orders,
    SUM(o.total_amount) as total_spent,
    AVG(o.total_amount) as avg_order_value,
    MAX(o.created_at) as last_order_date,
    p.name as favorite_product
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
LEFT JOIN order_items oi ON o.id = oi.order_id
LEFT JOIN products p ON oi.product_id = p.id
WHERE u.email LIKE '%@gmail.com'
    AND u.created_at >= '2023-01-01'
    AND o.status = 'completed'
GROUP BY u.id, u.name, u.email, u.created_at, p.name
HAVING total_orders > 5
ORDER BY total_spent DESC, u.name ASC
LIMIT 100;
