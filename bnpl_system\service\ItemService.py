from typing import List, Tuple, Dict
from bnpl_system.models.Item import Item

class ItemService:
    def __init__(self, item_repository: Dict[str, Item] = None):
        self.item_repository = item_repository or {}

    def seed_inventory(self, items: List[Tuple[str, int, float]]) -> None:
        for name, count, price in items:
            self.item_repository[name] = Item(name=name, price=price, count=count)

    def view_inventory(self) -> None:
        if not self.item_repository:
            print("Inventory is empty")
            return
        print("\n--- INVENTORY ---")
        for item in self.item_repository.values():
            print(f"{item.name}: price={item.price}, stock={item.count}")

    def add_inventory(self, item_name: str, count: int, price: float):
        try:
            if item_name in self.item_repository:
                self.item_repository[item_name].count += count
            else:
                self.item_repository[item_name] = Item(name=item_name, price=price, count=count)
            print(f"Added {count} {item_name} to inventory")
        except Exception as e:
            print(f"Error adding inventory: {e}")

    def remove_inventory(self, item_name: str, count: int):
        try:
            if item_name in self.item_repository:
                self.item_repository[item_name].count -= count
                if self.item_repository[item_name].count <= 0:
                    del self.item_repository[item_name]
            else:
                print(f"Item {item_name} not found")
            print(f"Removed {count} {item_name} from inventory")
        except Exception as e:
            print(f"Error removing inventory: {e}")
