import threading
import queue
from typing import Any

class MessageQueue:
    def __init__(self, max_size: int = 0):
        self.queue = queue.Queue(max_size)
        self.lock = threading.Lock()

    def enqueue(self, message: Any) -> None:
        with self.lock:
            self.queue.put(message)
            print(f"Enqueued: {message}")

    def dequeue(self) -> Any:
        with self.lock:
            if not self.queue.empty():
                message = self.queue.get()
                print(f"Dequeued: {message}")
                return message
            else:
                print("Queue is empty")
                return None

# Producer class
class Producer(threading.Thread):
    def __init__(self, message_queue: MessageQueue, messages: list):
        threading.Thread.__init__(self)
        self.message_queue = message_queue
        self.messages = messages

    def run(self) -> None:
        for message in self.messages:
            self.message_queue.enqueue(message)
            threading.Event().wait(0.1)  # Simulate some delay

# Consumer class
class Consumer(threading.Thread):
    def __init__(self, message_queue: MessageQueue, consume_count: int):
        threading.Thread.__init__(self)
        self.message_queue = message_queue
        self.consume_count = consume_count

    def run(self) -> None:
        for _ in range(self.consume_count):
            message = self.message_queue.dequeue()
            threading.Event().wait(0.1)  # Simulate some processing time

# Example usage
if __name__ == "__main__":
    message_queue = MessageQueue(max_size=10)
    
    # Create producers
    producer1 = Producer(message_queue, ["msg1", "msg2", "msg3"])
    producer2 = Producer(message_queue, ["msg4", "msg5", "msg6"])
    
    # Create consumers
    consumer1 = Consumer(message_queue, 3)
    consumer2 = Consumer(message_queue, 3)
    
    # Start producers and consumers
    producer1.start()
    producer2.start()
    consumer1.start()
    consumer2.start()
    
    # Join threads to the main thread
    producer1.join()
    producer2.join()
    consumer1.join()
    consumer2.join()
