from datetime import date, timedelta
from PaymentMethod import PaymentMethod
from BNPLApp import BNPLApp


if __name__ == "__main__":
    app = BNPLApp()
    app.seed_inventory([
        ("Laptop", 10, 500.0),
        ("Mobile", 20, 100.0),
        ("Headphones", 15, 20.0),
    ])

    print("\n--- Viewing Inventory ---")
    app.view_inventory()

    print("\n--- Registering Users ---")
    app.register_user("Ashutosh", credit_limit=5000.0)
    app.register_user("<PERSON>", credit_limit=10000.0)

    print("\n--- Placing Orders ---")
    app.buy(
        user_id="Ashutosh",
        cart=[("Laptop", 1), ("Headphones", 1)],
        payment_method=PaymentMethod.BNPL,
        purchase_date=date.today()
    )

    app.buy(
        user_id="Ashutosh", 
        cart=[("Mobile", 3)],
        payment_method=PaymentMethod.PREPAID,
        purchase_date=date.today()
    )

    app.buy(
        user_id="<PERSON>",
        cart=[("Mobile", 1), ("Headphones", 1)],
        payment_method=PaymentMethod.BNPL,
        purchase_date=date.today() - timedelta(days=40)
    )

    print("\n--- Checking Dues ---")
    app.view_dues("Ashutosh", date.today())
    app.view_dues("John", date.today())   

    print("\n--- Order Status ---")  
    app.order_status("Ashutosh")
    app.order_status("John")
    clearing_date = date.today() + timedelta(days=31)

    print("\n--- Clearing Dues ---")
    app.clear_dues("Ashutosh", [1], clearing_date)
    app.clear_dues("John", [3], clearing_date)

    print("\n--- Checking Dues After Clearing ---")
    app.view_dues("Ashutosh", clearing_date)
    app.view_dues("John", clearing_date)     

    print("\n--- Order Status After Clearing ---")
    app.order_status("Ashutosh")
    app.order_status("John")

    print("\n--- Viewing Inventory After Orders ---")
    app.view_inventory()
    