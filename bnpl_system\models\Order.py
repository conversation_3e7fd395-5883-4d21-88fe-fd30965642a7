from typing import List, Tuple, Optional
from datetime import date
from bnpl_system.PaymentMethods import PaymentMethod

class Order:
    def __init__(self, order_id: str, user_id: str, items: List[Tuple[str, int]],
                 total_amount: float, payment_method: PaymentMethod, purchase_date: date):
        self.order_id = order_id
        self.user_id = user_id
        self.items = items
        self.total_amount = total_amount
        self.payment_method = payment_method
        self.purchase_date = purchase_date
        self.cleared = False
        self.cleared_date: Optional[date] = None

    def is_due(self, on_date: date) -> bool:
        return (
            self.payment_method == PaymentMethod.BNPL
            and not self.cleared
            and self.purchase_date <= on_date
        )

    def status_on(self, on_date: date) -> str:
        if not self.is_due(on_date):
            return "CLEARED"
        overdue = (on_date - self.purchase_date).days
        if overdue > 30:
            return "DELAYED"
        return "PENDING"