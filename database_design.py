class Column:
  def __init__(self, name, data_type, max_length=None, min_value=None, required=False):
    self.name = name
    self.data_type = data_type
    self.max_length = max_length
    self.min_value = min_value
    self.required = required

  def validate(self, value):
    if self.required and value is None:
      raise ValueError(f"Column '{self.name}' is mandatory.")
    if self.data_type == "string" and self.max_length and len(value) > self.max_length:
      raise ValueError(f"String value for '{self.name}' exceeds maximum length ({self.max_length})")
    if self.data_type == "int" and self.min_value and value < self.min_value:
      raise ValueError(f"Integer value for '{self.name}' must be greater than or equal to {self.min_value}")

class Table:
  def __init__(self, name, columns):
    self.name = name
    self.columns = {col.name: col for col in columns}
    self.data = []

  def insert(self, data):
    for col_name, value in data.items():
      self.columns[col_name].validate(value)
    self.data.append(data)

  def print_all(self):
    if not self.data:
      print(f"Table '{self.name}' is empty.")
      return
    headers = [col.name for col in self.columns.values()]
    print("|", " | ".join(headers), "|")
    print("-" * (len(headers) * 3 + 2))
    for row in self.data:
      print("|", " | ".join([str(row[col]) for col in headers]), "|")

  def filter(self, criteria):
    filtered_data = [row for row in self.data if all(row[col] == criteria[col] for col in criteria)]
    if not filtered_data:
      print(f"No records found in table '{self.name}' matching the criteria.")
      return
    headers = [col for col in criteria]
    print("|", " | ".join(headers), "|")
    print("-" * (len(headers) * 3 + 2))
    for row in filtered_data:
      print("|", " | ".join([str(row[col]) for col in headers]), "|")

class Database:
  def __init__(self):
    self.tables = {}

  def create_table(self, table_name, columns):
    if table_name in self.tables:
      raise ValueError(f"Table '{table_name}' already exists.")
    self.tables[table_name] = Table(table_name, columns)

  def insert(self, table_name, data):
    if table_name not in self.tables:
      raise ValueError(f"Table '{table_name}' does not exist.")
    self.tables[table_name].insert(data)

  def print_table(self, table_name):
    if table_name not in self.tables:
      raise ValueError(f"Table '{table_name}' does not exist.")
    self.tables[table_name].print_all()

  def filter_table(self, table_name, criteria):
    if table_name not in self.tables:
      raise ValueError(f"Table '{table_name}' does not exist.")
    self.tables[table_name].filter(criteria)


# Example Usage
db = Database()
db.create_table(
    "users",
    [
        Column("id", "int", required=True),
        Column("name", "string", max_length=20, required=True),
        Column("email", "string"),
        Column("age", "int", min_value=1024),
    ],
)
db.insert(
    "users", {"id": 1, "name": "Alice", "email": "<EMAIL>", "age": 1234}
)
db.insert(
    "users", {"id": 2, "name": "Bob", "email": "<EMAIL>", "age": 1234}
)
# Try inserting with invalid data to trigger validation errors
try:
  db.insert("users", {"name": "Charlie", "age": 1000})  # Missing ID and age below minimum
except ValueError as e:
  print(e)
# Print all users
db.print_table("users")
# Filter users by name
db.filter_table("users", {"name": "Alice"})