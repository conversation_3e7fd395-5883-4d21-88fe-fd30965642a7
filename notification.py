import random

class Notification:
    def __init__(self, message, recipient):
        self.message = message
        self.recipient = recipient
        self.channels = [EmailNotification(), SMSNotification()]  # Add more channels here

    def send(self):
        for channel in self.channels:
            try:
                if channel.send(self.message, self.recipient):
                    print(f"Notification sent successfully via {channel.name}.")
                    return True  # Exit loop on successful notification
            except Exception as e:
                print(f"Error sending notification via {channel.name}: {e}")
        print("Failed to send notification through all channels.")
        return False

class NotificationChannel:
    def __init__(self, name):
        self.name = name

    def send(self, message, recipient):
        raise NotImplementedError("Subclasses must implement send method")

class EmailNotification(NotificationChannel):
    def __init__(self):
        super().__init__("Email")

    def send(self, message, recipient):
        # Simulate email sending (replace with your actual email sending logic)
        print(f"Sending email notification: {message} to {recipient}")
        # Implement logic to track success rate here (e.g., using a success flag or exception handling)
        success_rate = 0.9  # Replace with actual success rate calculation
        return random.random() < success_rate  # Simulate random success/failure

class SMSNotification(NotificationChannel):
    def __init__(self):
        super().__init__("SMS")

    def send(self, message, recipient):
        # Simulate SMS sending (replace with your actual SMS sending logic)
        print(f"Sending SMS notification: {message} to {recipient}")
        # Implement logic to track success rate here (e.g., using a success flag or exception handling)
        success_rate = 0.8  # Replace with actual success rate calculation
        return random.random() < success_rate  # Simulate random success/failure

# Example usage
notification = Notification("This is a test notification", "<EMAIL>")
notification.send()
