class Document:
  """
  Represents a document with an ID and content.
  """
  def __init__(self, doc_id, content):
    self.id = doc_id
    self.content = content.lower()  # Convert content to lowercase for case-insensitive search

class InMemorySearchEngine:
  """
  A simple in-memory search engine for text documents.
  """
  def __init__(self):
    self.datasets = {}  # Dictionary to store datasets (key: dataset name, value: list of documents)

  def create_dataset(self, dataset_name):
    """
    Creates a new dataset.
    """
    if dataset_name in self.datasets:
      raise ValueError(f"Dataset '{dataset_name}' already exists.")
    self.datasets[dataset_name] = []

  def insert_document(self, dataset_name, document):
    """
    Inserts a document into a specific dataset.
    """
    if dataset_name not in self.datasets:
      raise ValueError(f"Dataset '{dataset_name}' does not exist.")
    self.datasets[dataset_name].append(document)

  def search(self, dataset_name, search_term, sort_by_relevance=True):
    """
    Searches for documents containing the search term in a dataset.

    Args:
      dataset_name: The name of the dataset to search.
      search_term: The term to search for.
      sort_by_relevance: If True, sort results by relevance (documents with more occurrences first).

    Returns:
      A list of document IDs matching the search query.
    """
    if dataset_name not in self.datasets:
      raise ValueError(f"Dataset '{dataset_name}' does not exist.")

    search_term = search_term.lower()  # Convert search term to lowercase for case-insensitive search
    results = []
    for doc in self.datasets[dataset_name]:
      if search_term in doc.content:
        # Count term occurrences for relevance scoring (if enabled)
        if sort_by_relevance:
          count = doc.content.count(search_term)
        else:
          count = 1
        results.append((doc.id, count))

    # Sort results by relevance (descending order) or document ID (ascending order)
    if sort_by_relevance:
      results.sort(key=lambda x: x[1], reverse=True)  # Sort by count (relevance) descending
    else:
      results.sort(key=lambda x: x[0])  # Sort by document ID ascending

    # Return only document IDs
    return [result[0] for result in results]

# Usage Example
search_engine = InMemorySearchEngine()

search_engine.create_dataset("blog_posts")

search_engine.insert_document("blog_posts", Document(1, "Apple is a fruit. It is a popular choice for health conscious people."))
search_engine.insert_document("blog_posts", Document(2, "This blog post is about apple picking season. Come join us for some fun!"))
search_engine.insert_document("blog_posts", Document(3, "Here are some tips for growing oranges at home. They might be a bit sour, but rich in Vitamin C!"))
search_engine.insert_document("blog_posts", Document(4, "Did you know there are many different varieties of apples? Some are sweet, some are tart."))
search_engine.insert_document("blog_posts", Document(5, "Healthy eating habits include incorporating a variety of vegetables in your diet."))

search_results = search_engine.search("blog_posts", "apple", sort_by_relevance=True)
print(f"Search results for 'apple' (sorted by relevance): {search_results}")  # Output: [2, 1, 4]

search_results = search_engine.search("blog_posts", "orange", sort_by_relevance=False)
print(f"Search results for 'orange': {search_results}")  # Output: [3]

search_results = search_engine.search("blog_posts", "healthy", sort_by_relevance=True)
print(f"Search results for 'healthy' (sorted by relevance): {search_results}")  # Output: [5, 1]
