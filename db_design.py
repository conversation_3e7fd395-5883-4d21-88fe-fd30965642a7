class Database:
    def __init__(self):
        self.tables = {}
    
    def create_table(self, table, columns):
        if table in self.tables:
            raise ValueError(f" table {table} already exists")
        self.tables[table] = Table(table, columns)

    def insert(self, table, data):
        if table not in self.tables:
            raise ValueError(f" table {table} does not exists")
        self.tables[table].insert(data)

    def print_table(self, table):
        if table not in self.tables:
            raise ValueError(f" table {table} does not exists")
        self.tables[table].print_all()

    def filter_table(self, table, criteria):
        if table not in self.tables:
            raise ValueError(f" table {table} does not exists")
        self.tables[table].filter(criteria)

class Table:
    def __init__(self, name, columns):
        self.name = name
        self.columns = {col.name: col for col in columns}
        self.data = []

    def insert(self, data):
        for col_name, value in data.items():
            self.columns[col_name].validate(value)
        self.data.append(data)

    def print_all(self):
        if not self.data:
            print(f"table {self.name} is empty")
            return
        headers = [col.name for col in self.columns.values()]

        print("|", "|".join(headers), "|")
        print("-" * (len(headers) * 3 + 2))
        for row in self.data:
            print("|", "|".join([str(row[col]) for col in headers]), "|")

    def filter(self, criteria):
        filtered_data = [row for row in self.data if all(row[col] == criteria[col] for col in criteria)]
        if not filtered_data:
            print(f"table {self.name} is empty")
            return
        
        headers = [col.name for col in self.columns.values()]

        print("|", "|".join(headers), "|")
        print("-" * (len(headers) * 3 + 2))
        for row in filtered_data:
            print("|", "|".join([str(row[col]) for col in headers]), "|")

class Column:
    def __init__(self, name, data_type, max_length=None, min_value=None, required=False):
        self.name = name
        self.data_type = data_type
        self.max_length = max_length
        self.min_value = min_value
        self.required = required

    def validate(self, value):
        if self.required and value is None:
            raise ValueError(f"column {self.name} is mandatory")
        if self.data_type == "string" and self.max_length and len(value) > self.max_length:
            raise ValueError(f"length of {self.name} exceeds length")
        if self.data_type == "int" and self.min_value and value < self.min_value:
            raise ValueError(f"length of {self.name} is less than the minimum value")


db = Database()

db.create_table(
    "users",
    [Column("id", "int", required=True),
    Column("name", "string", max_length=20, required=True),
    Column("email", "string"),
    Column("age", "int", min_value=10)]
)

db.insert("users", {"id": 1, "name": "alice", "email": "alice.gmail.com", "age": 25})

db.insert("users", {"id": 2, "name": "bob", "email": "bob.gmail.com", "age": 35})

try:
    db.insert("users", {"id": 3, "name": "Charles", "age": 5})
except ValueError as e:
    print(e)

db.print_table("users")

db.filter_table("users", {"name": "alice"})

