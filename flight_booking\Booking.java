package flight_booking;

public class Booking {
    private String userId;
    private String flightNumber;
    private String date;

    public Booking(String userId, String flightNumber, String date){
        this.userId = userId;
        this.flightNumber = flightNumber;
        this.date = date;
    }

    public String getUserId() { return userId; }
    public String getFlightNumber() { return flightNumber; }
    public String getDate() { return date; }

    @Override
    public String toString(){
        return String.format("Booking [User: %s | Flight %s | Date %s]", userId, flightNumber, date);
    }
}
