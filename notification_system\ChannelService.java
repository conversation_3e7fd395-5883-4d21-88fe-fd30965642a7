package notification_system;

import java.util.HashMap;

public class ChannelService {
    private Channel channel;
    private HashMap<Integer, Channel> channels;

    public ChannelService(Channel channel) {
        this.channel = channel;
        this.channels = new HashMap<>();
    }
    public ChannelType getChannel(Channel channel) {
        return channel.getChannelType();
    }

    public synchronized String addChannel(int userId, ChannelType channelType) {
        Channel newChannel = new Channel(channelType);
        if (channels.containsKey(userId) && channels.get(userId).isActive()) {
            return "Channel already exists for user " + userId;
        }
        channels.put(userId, newChannel);
        newChannel.setActive(true);
        return "Channel " + channelType + " added for user " + userId;
    }
    public synchronized String removeChannel(int userId, ChannelType channelType) {
        Channel existingChannel = channels.get(userId);
        if (existingChannel != null && existingChannel.getChannelType() == channelType) {
            existingChannel.setActive(false);
            return "Channel " + channelType + " removed for user " + userId;
        }
        return "Channel not found for user " + userId;
    }

    //notify user when channel is modified
    public String notifyChannelModification(int userId, ChannelType channelType, String action) {
        return "User " + userId + ": Channel " + channelType + " has been " + action;
    }
    public String notifyChannelModification(int userId, ChannelType channelType) {
        return notifyChannelModification(userId, channelType, "modified");
    }
    
}
