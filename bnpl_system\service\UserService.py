from typing import Dict
from bnpl_system.models.User import User

class UserService:
    def __init__(self):
        self.users: Dict[str, User] = {}

    def register_user(self, user_id: str, credit_limit: float) -> User:
        if user_id in self.users:
            raise ValueError(f"User {user_id} already exists")
        
        user = User(user_id, credit_limit)
        self.users[user_id] = user
        return user

    def get_user(self, user_id: str) -> User:
        if user_id not in self.users:
            raise ValueError(f"User {user_id} not found")
        return self.users[user_id]