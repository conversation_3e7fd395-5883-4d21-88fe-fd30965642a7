import java.util.*;

public class foodOrderingSystem {
    private Map<String, Customer> customers = new HashMap<>();
    private Map<String, Restaurant> restaurants = new HashMap<>();
    private Map<String, List<Order>> customerOrders = new HashMap<>();

    public void registerCustomer(String name, String id){
        customers.put(id, new Customer(name, id));
    }

    public void registerRestaurant(String name, String id){
        restaurants.put(id, new Restaurant(name, id));
    }

    public void addItemToRestaurant(String restaurantId, String itemId, String name, double price, int qty){
        Restaurant r = restaurants.get(restaurantId);
        if (r != null) r.addItem(itemId, name, price, qty);
    }

    public List<Item> searchItems(String restaurantId, String itemName){
        Restaurant r = restaurants.get(restaurantId);
        if (r != null) return r.searchItem(itemName);
        return Collections.emptyList();
    }

    public void placeOrder(String customerId, String restaurantId, Map<String, Integer> orderItems){
        Customer c = customers.get(customerId);
        Restaurant r = restaurants.get(restaurantId);

        if (c == null || r == null) throw new IllegalArgumentException("Customer or Restaurant not found");

        Map<Item, Integer> itemsMap = new HashMap<>();
        for (String itemId : orderItems.keySet()){
            Item item = r.getItemById(itemId);
            if (item == null) throw new IllegalArgumentException("Item not found: " + itemId);
            int qty = orderItems.get(itemId);
            if (qty > item.getQuantity()) throw new IllegalArgumentException("Insufficient quantity for: " + item.getName());
            item.reduceQuantity(qty);
            itemsMap.put(item, qty);
        }

        Order order = new Order(c, r, itemsMap);
        customerOrders.computeIfAbsent(customerId, k -> new ArrayList<>()).add(order);
        System.out.println("Order placed successfully: " + order.getOrderId());
    }

    public List<Order> getOrders(String customerId){
        return customerOrders.getOrDefault(customerId, Collections.emptyList());
    }

    public void cancelOrder(String customerId, String orderId){
        List<Order> orders = customerOrders.get(customerId);
        if (orders != null){
            for (Order order : orders){
                if (order.getOrderId().equals(orderId)){
                    order.cancel();
                    System.out.println("Order cancelled successfully: " + orderId);
                    return ;
                }
            }
        }
        System.out.println("Order not found or cannot be cancelled.");
    }
}
