"""
Nandi Hills Video Concatenator - No Duplicates

This script specifically handles your Nandi Hills videos and removes duplicates
before concatenation.
"""

import cv2
import os
import glob

def get_unique_video_files(directory):
    """Get unique video files from directory, removing duplicates."""
    extensions = ['*.mp4', '*.MP4', '*.avi', '*.AVI', '*.mov', '*.MOV']
    
    all_files = []
    for ext in extensions:
        all_files.extend(glob.glob(os.path.join(directory, ext)))
    
    # Remove duplicates while preserving chronological order
    unique_files = []
    seen_basenames = set()
    
    for file_path in sorted(all_files):
        basename = os.path.basename(file_path)
        if basename not in seen_basenames:
            seen_basenames.add(basename)
            unique_files.append(file_path)
    
    return unique_files

def concatenate_nandi_hills_videos(video_dir, output_filename="nandi_hills_no_duplicates.mp4"):
    """Concatenate Nandi Hills videos without duplicates."""
    
    print("🏔️ Nandi Hills Video Concatenator (No Duplicates)")
    print("=" * 55)
    
    # Get unique video files
    video_files = get_unique_video_files(video_dir)
    
    if not video_files:
        print("❌ No video files found in the directory")
        return False
    
    print(f"📁 Found {len(video_files)} unique videos:")
    for i, file in enumerate(video_files[:10], 1):  # Show first 10
        print(f"  {i}. {os.path.basename(file)}")
    
    if len(video_files) > 10:
        print(f"  ... and {len(video_files) - 10} more videos")
    
    # Get output path
    output_path = os.path.join(video_dir, output_filename)
    
    print(f"\n🎬 Starting concatenation...")
    print(f"📤 Output: {output_filename}")
    print(f"⏱️  Estimated time: {len(video_files) * 2 // 60} minutes")
    
    try:
        # Get video properties from first video
        cap = cv2.VideoCapture(video_files[0])
        if not cap.isOpened():
            print(f"❌ Cannot read first video: {video_files[0]}")
            return False
        
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = int(cap.get(cv2.CAP_PROP_FPS)) or 30
        cap.release()
        
        print(f"📺 Video properties: {width}x{height} @ {fps} FPS")
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ Failed to create output video writer")
            return False
        
        total_frames = 0
        
        # Process each unique video
        for i, video_path in enumerate(video_files, 1):
            print(f"🎥 Processing {i}/{len(video_files)}: {os.path.basename(video_path)}")
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"⚠️  Warning: Could not open {video_path}")
                continue
            
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Resize if necessary
                if frame.shape[1] != width or frame.shape[0] != height:
                    frame = cv2.resize(frame, (width, height))
                
                out.write(frame)
                frame_count += 1
                total_frames += 1
                
                # Progress indicator for large videos
                if frame_count % 500 == 0:
                    print(f"    📊 {frame_count} frames processed...", end='\r')
            
            cap.release()
            duration = frame_count / fps
            print(f"    ✅ Added {frame_count} frames ({duration:.1f}s)")
        
        out.release()
        
        # Calculate final video duration
        final_duration = total_frames / fps
        final_minutes = int(final_duration // 60)
        final_seconds = int(final_duration % 60)
        
        print(f"\n🎉 Concatenation completed successfully!")
        print(f"📊 Final video stats:")
        print(f"   • Total frames: {total_frames:,}")
        print(f"   • Duration: {final_minutes}m {final_seconds}s")
        print(f"   • File: {output_filename}")
        print(f"   • Location: {video_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during concatenation: {e}")
        return False

def main():
    """Main function."""
    # Your Nandi Hills video directory
    nandi_hills_dir = r"C:\Users\<USER>\Downloads\nandi_hills"
    
    # Check if directory exists
    if not os.path.isdir(nandi_hills_dir):
        print(f"❌ Directory not found: {nandi_hills_dir}")
        print("Please check the path and try again.")
        return
    
    # Start concatenation
    success = concatenate_nandi_hills_videos(nandi_hills_dir)
    
    if success:
        print("\n🏔️ Your Nandi Hills video compilation is ready!")
        print("🎬 Enjoy your trip memories!")
    else:
        print("\n❌ Concatenation failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
