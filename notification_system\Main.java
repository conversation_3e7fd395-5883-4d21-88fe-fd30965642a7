package notification_system;

public class Main {
    public static void main(String[] args) {

        Customer customer = new Customer(1, "<PERSON>", "<EMAIL>", "1234567890", 101);
        Seller seller = new Seller(2, "<PERSON>", "<EMAIL>", "0987654321", 201);
        Logistics logistics = new Logistics(3, "<PERSON>", "<EMAIL>", "1357924680", 301);
        EventService eventService = new EventService();
        ChannelService channelService = new ChannelService(new Channel(ChannelType.EMAIL));
    
        System.out.println("\n");


        System.out.println("Logistics notification when order is shipped");
        System.out.println(logistics.notifyLogistics(logistics.getUserId(), 1001, new Event(EventType.SHIPPED, 1001, 1, 2, 3, "Order out for delivery"), ChannelType.EMAIL));
        System.out.println("Seller notification when order is placed");
        System.out.println(seller.notifySeller(seller.getUserId(), 1001, new Event(EventType.PLACED, 1001, 1, 2, 3, "Order placed"), ChannelType.EMAIL));
        System.out.println("Customer notification when order is delivered");
        System.out.println(customer.notifyCustomer(customer.getUserId(), 1001, new Event(EventType.DELIVERED, 1001, 1, 2, 3, "Order delivered"), ChannelType.EMAIL));
        System.out.println("Seller notification when order is shipped (should be none)");
        System.out.println(seller.notifySeller(seller.getUserId(), 1001, new Event(EventType.SHIPPED, 1001, 1, 2, 3, "Order out for delivery"), ChannelType.EMAIL));
        System.out.println("Logistics notification when order is delivered (should be none)");
        System.out.println(logistics.notifyLogistics(logistics.getUserId(), 1001, new Event(EventType.DELIVERED, 1001, 1, 2, 3, "Order delivered"), ChannelType.EMAIL));
        System.out.println("Customer notification when order is placed");
        System.out.println(customer.notifyCustomer(customer.getUserId(), 1001, new Event(EventType.PLACED, 1001, 1, 2, 3, "Order placed"), ChannelType.EMAIL));
        System.out.println("Seller notification when order is delivered (should be none)");
        System.out.println(seller.notifySeller(seller.getUserId(), 1001, new Event(EventType.DELIVERED, 1001, 1, 2, 3, "Order delivered"), ChannelType.EMAIL));
        System.out.println("Logistics notification when order is placed (should be none)");
        System.out.println(logistics.notifyLogistics(logistics.getUserId(), 1001, new Event(EventType.PLACED, 1001, 1, 2, 3, "Order placed"), ChannelType.EMAIL));
        System.out.println("Customer notification when order is shipped");
        System.out.println(customer.notifyCustomer(customer.getUserId(), 1001, new Event(EventType.SHIPPED, 1001, 1, 2, 3, "Order out for delivery"), ChannelType.EMAIL));
        System.out.println("Seller notification when order is delivered (should be none)");
        System.out.println(seller.notifySeller(seller.getUserId(), 1001, new Event(EventType.DELIVERED, 1001, 1, 2, 3, "Order delivered"), ChannelType.EMAIL));
        System.out.println("Logistics notification when order is placed (should be none)");
        System.out.println(logistics.notifyLogistics(logistics.getUserId(), 1001, new Event(EventType.PLACED, 1001, 1, 2, 3, "Order placed"), ChannelType.EMAIL));
        System.out.println("Customer notification when order is delivered");
        System.out.println(customer.notifyCustomer(customer.getUserId(), 1001, new Event(EventType.DELIVERED, 1001, 1, 2, 3, "Order delivered"), ChannelType.EMAIL));
        System.out.println("Seller notification when order is delivered (should be none)");
        System.out.println(seller.notifySeller(seller.getUserId(), 1001, new Event(EventType.DELIVERED, 1001, 1, 2, 3, "Order delivered"), ChannelType.EMAIL));

        System.out.println("\nEvent subscription and unsubscription");
        System.out.println(eventService.subscribeEvent(customer.getUserId(), EventType.PLACED, ChannelType.EMAIL));
        System.out.println(eventService.unsubscribeEvent(customer.getUserId(), EventType.PLACED, ChannelType.EMAIL));
        System.out.println(eventService.subscribeEvent(seller.getUserId(), EventType.PLACED, ChannelType.EMAIL));
        System.out.println(eventService.unsubscribeEvent(seller.getUserId(), EventType.PLACED, ChannelType.EMAIL));
        System.out.println(eventService.subscribeEvent(logistics.getUserId(), EventType.SHIPPED, ChannelType.EMAIL));
        System.out.println(eventService.unsubscribeEvent(logistics.getUserId(), EventType.SHIPPED, ChannelType.EMAIL));
        System.out.println(eventService.subscribeEvent(customer.getUserId(), EventType.DELIVERED, ChannelType.SMS));
        System.out.println(eventService.unsubscribeEvent(customer.getUserId(), EventType.DELIVERED, ChannelType.SMS));
        System.out.println(eventService.subscribeEvent(customer.getUserId(), EventType.PLACED, ChannelType.APP));
        System.out.println(eventService.unsubscribeEvent(customer.getUserId(), EventType.PLACED, ChannelType.APP));

        System.out.println("\nChannel subscription and unsubscription without concurrency:");
        System.out.println(channelService.addChannel(0, ChannelType.SMS));
        System.out.println(channelService.removeChannel(0, ChannelType.SMS));
        System.out.println(channelService.addChannel(0, ChannelType.APP));
        System.out.println(channelService.removeChannel(0, ChannelType.APP));
        System.out.println(channelService.addChannel(0, ChannelType.EMAIL));
        System.out.println(channelService.removeChannel(1, ChannelType.EMAIL));
        System.out.println(channelService.addChannel(1, ChannelType.SMS));
        System.out.println(channelService.removeChannel(1, ChannelType.SMS));

        
        System.out.println("\nEvent subscription and unsubscription with concurrency:");
        Runnable task3 = () -> {
            System.out.println(eventService.subscribeEvent(seller.getUserId(), EventType.DELIVERED, ChannelType.SMS));
        };
        Runnable task4 = () -> {
            System.out.println(eventService.unsubscribeEvent(seller.getUserId(), EventType.DELIVERED, ChannelType.SMS));
        };
        Thread thread3 = new Thread(task3);
        Thread thread4 = new Thread(task4);
        thread3.start();
        thread4.start();
        try {
            thread3.join();
            thread4.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
            System.out.println("Thread interrupted");
        }


    }
}
