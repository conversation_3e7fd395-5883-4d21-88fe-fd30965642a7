package cab_booking;

import java.util.HashMap;
import java.util.Map;

public class DriverService {
    private Map<String, Driver> driverMap;

    public DriverService(Map<String, Driver> driverMap){
        this.driverMap = new HashMap<>();
    }

    public Map<String, Driver> getDriverMap() {return driverMap; }

    public String addDriver(String name, String driverId, Location location){
        Driver driver = new Driver(name, driverId, location);
        if (driverMap.containsKey(driverId)){
            throw new IllegalArgumentException("Driver already exists: " + driverId);
        }
        driverMap.put(driverId, driver);
        return "Driver: " + driverId + " successfully registered.";
    }
    
}
