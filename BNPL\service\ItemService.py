# BNPL/service/ItemService.py
from typing import List, Tuple, Dict
from BNPL.models.Item import Item

class ItemService:
    def __init__(self, item_repository: Dict[str, Item] = None):
        self.item_repository = item_repository or {}

    def seed_inventory(self, items: List[Tuple[str, int, float]]) -> None:
        for name, count, price in items:
            self.item_repository[name] = Item(name=name, price=price, count=count)

    def view_inventory(self) -> None:
        for item in self.item_repository.values():
            print(f"{item.name}: price=${item.price}, stock={item.count}")

    def add_item(self, name: str, count: int, price: float) -> None:
        if name in self.item_repository:
            self.item_repository[name].count += count
            self.item_repository[name].price = price  # Update price if needed
        else:
            self.item_repository[name] = Item(name=name, price=price, count=count)

    def remove_item(self, name: str, count: int) -> None:
        if name in self.item_repository:
            self.item_repository[name].count -= count
            if self.item_repository[name].count <= 0:
                del self.item_repository[name]