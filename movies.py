"""
Video Concatenation Tool

This script concatenates multiple video files into a single output video.
Supports various video formats and provides flexible input options.

Requirements:
- moviepy: pip install moviepy

Usage:
1. Run script and enter directory path when prompted
2. Pass directory as command line argument: python movies.py /path/to/videos
3. Use the concatenate_videos function directly in your code
"""

from moviepy.editor import VideoFileClip, concatenate_videoclips
import os
import sys
import glob

def concatenate_videos(video_paths, output_path, method="compose", verbose=True):
    """
    Concatenate multiple video files into a single video.

    Args:
        video_paths (list): List of paths to video files to concatenate
        output_path (str): Path for the output video file
        method (str): Concatenation method - "compose" or "chain"
        verbose (bool): Whether to print progress information

    Returns:
        bool: True if successful, False otherwise
    """
    if not video_paths:
        print("Error: No video files provided")
        return False

    if verbose:
        print(f"Concatenating {len(video_paths)} videos...")
        for i, path in enumerate(video_paths, 1):
            print(f"  {i}. {os.path.basename(path)}")

    try:
        # Load video clips
        clips = []
        for path in video_paths:
            if not os.path.exists(path):
                print(f"Warning: File not found: {path}")
                continue
            clips.append(VideoFileClip(path))

        if not clips:
            print("Error: No valid video files found")
            return False

        # Concatenate videos
        final_clip = concatenate_videoclips(clips, method=method)

        # Write output file
        if verbose:
            print(f"Writing output to: {output_path}")
        final_clip.write_videofile(output_path, verbose=verbose, logger='bar' if verbose else None)

        # Clean up
        for clip in clips:
            clip.close()
        final_clip.close()

        if verbose:
            print("✅ Video concatenation completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error during video concatenation: {str(e)}")
        return False

def get_video_files(directory, extensions=None):
    """
    Get all video files from a directory.

    Args:
        directory (str): Directory path to search
        extensions (list): List of video file extensions to include

    Returns:
        list: Sorted list of video file paths
    """
    if extensions is None:
        extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.wmv', '*.flv', '*.webm']

    video_files = []
    for ext in extensions:
        video_files.extend(glob.glob(os.path.join(directory, ext)))
        video_files.extend(glob.glob(os.path.join(directory, ext.upper())))

    return sorted(video_files)

def main():
    """Main function to handle command line usage."""
    print("🎬 Video Concatenation Tool")
    print("=" * 40)

    # Get directory path from user input or command line argument
    if len(sys.argv) > 1:
        video_dir = sys.argv[1]
    else:
        video_dir = input("Enter the directory containing videos: ").strip()

    # Validate directory
    if not os.path.isdir(video_dir):
        print(f"❌ Error: Directory '{video_dir}' does not exist.")
        sys.exit(1)

    # Find video files
    video_files = get_video_files(video_dir)

    if not video_files:
        print("❌ No video files found in the specified directory.")
        print("Supported formats: MP4, AVI, MOV, MKV, WMV, FLV, WEBM")
        sys.exit(1)

    print(f"\n📁 Found {len(video_files)} video files:")
    for i, file in enumerate(video_files, 1):
        print(f"  {i}. {os.path.basename(file)}")

    # Get output filename
    output_filename = input(f"\nEnter output filename (default: concatenated_video.mp4): ").strip()
    if not output_filename:
        output_filename = "concatenated_video.mp4"

    # Ensure output has proper extension
    if not output_filename.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
        output_filename += '.mp4'

    output_file = os.path.join(video_dir, output_filename)

    # Check if output file already exists
    if os.path.exists(output_file):
        overwrite = input(f"⚠️  Output file '{output_filename}' already exists. Overwrite? (y/N): ").strip().lower()
        if overwrite != 'y':
            print("Operation cancelled.")
            sys.exit(0)

    # Concatenate videos
    success = concatenate_videos(video_files, output_file)

    if success:
        print(f"\n🎉 Success! Concatenated video saved as: {output_filename}")
    else:
        print("\n❌ Failed to concatenate videos.")
        sys.exit(1)

if __name__ == "__main__":
    main()
