class Admin:
    def __init__(self, id, name, email):
        self.id = id
        self.name = name
        self.email = email

class Voucher:
    def __init__(self, id, voucher_id, user_id=None, used=None):
        self.id = id
        self.voucher_id = voucher_id
        self.user_id = user_id
        self.used = used

class Coupon():
    def __init__(self, id, avg_limit, exp_date, status):
        self.id = id
        self.avg_limit = avg_limit
        self.exp_date = exp_date
        self.status = status
    
class CouponService:
    def create_coupon(self, avg_limit, exp_date, status):
        pass

    def delete_coupon(self, id):
        pass

    def update_coupon_status(self, id, status):
        pass

class VoucherService:
    def create_voucher(self, voucher_id, user_id=None):
        pass

    def assign_voucher(self, id, user_id):
        pass

    def use_status(self, id):
        pass