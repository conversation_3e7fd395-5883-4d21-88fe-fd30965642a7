package notification_system;

public class Customer extends User {
    private int customerId;

    public Customer(int userId, String userName, String email, String phoneNumber, int customerId) {
        super(userId, userName, email, phoneNumber);
        this.customerId = customerId;
    }

    public int getCustomerId() {
        return customerId;
    }

    //customer will get notified when order is placed, shipped, delivered.
    public String notifyCustomer(int userId, int orderId, Event event, ChannelType channel) {
        return "Customer " + userId + " notified for order " + orderId + " via " + channel.toString();
    }   
}
