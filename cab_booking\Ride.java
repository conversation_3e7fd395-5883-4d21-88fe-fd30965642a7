package cab_booking;

public class Ride {
    private String rideId;
    private Rider rider;
    private Driver driver;
    private Location startLocation;
    private Location endLocation;
    private RideStatus status;

    public Ride(String rideId, Rider rider, Driver driver, Location startLocation){
        this.rideId = rideId;
        this.rider = rider;
        this.driver = driver;
        this.startLocation = startLocation;
        this.endLocation = null;
        this.status = RideStatus.ONGOING;
    }

    public String getRideId() { return rideId; }
    public Rider getRider() { return rider; }
    public Driver getDriver() { return driver; }
    public Location getStartLocation() { return startLocation; }
    public Location getEndLocation() { return endLocation; }
    public RideStatus getStatus() { return status; }

    public void endRide(Location endLocation){
        this.endLocation = endLocation;
        this.status = RideStatus.COMPLETED;
        this.driver.setIsAvailable(true);
    }
}
