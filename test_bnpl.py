#!/usr/bin/env python3
"""
Simple test script for BNPL system
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_bnpl_system():
    print("🧪 Testing BNPL System Components...")
    
    try:
        # Test imports
        print("1. Testing imports...")
        from BNPL.service.UserService import UserService
        from BNPL.service.ItemService import ItemService
        from BNPL.service.OrderService import OrderService
        print("   ✅ All services imported successfully")
        
        # Test service creation
        print("2. Creating services...")
        item_service = ItemService()
        user_service = UserService()
        order_service = OrderService(user_service, item_service)
        print("   ✅ All services created successfully")
        
        # Test basic functionality
        print("3. Testing basic functionality...")
        
        # Seed inventory
        item_service.seed_inventory([
            ("Laptop", 5, 500.0),
            ("Mouse", 10, 25.0)
        ])
        print("   ✅ Inventory seeded")
        
        # Register user
        user = user_service.register_user("testuser", 1000.0)
        print(f"   ✅ User registered: {user}")
        
        # Place order
        cart = [("Laptop", 1), ("Mouse", 2)]
        order = order_service.place_order("testuser", cart, "BNPL", "2024-01-01")
        print(f"   ✅ Order placed: {order.order_id}")
        
        # View dues
        print("   📋 Viewing dues...")
        order_service.view_dues("testuser", "2024-02-01")
        
        print("\n🎉 All tests passed! BNPL system is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_bnpl_system()
    sys.exit(0 if success else 1)
