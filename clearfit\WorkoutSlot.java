package clearfit;
import java.util.*;

public class WorkoutSlot {
    private int capacity;
    private int availableSeats;
    private int duration;
    private TimeSlot timeslot;
    private Set<String> bookedUsers;

    public WorkoutSlot(int capacity, int availableSeats,
     int duration, TimeSlot timeslot, Set<String> bookedUsers){
        this.capacity = capacity;
        this.availableSeats = availableSeats;
        this.duration = duration;
        this.timeslot = timeslot;
        this.bookedUsers = new HashSet<>();
    }

    public int getCapacity() { return capacity; }
    public int getAvailableSeats() { return availableSeats; }
    public int getDuration() { return duration; }
    public TimeSlot getTimeSlot() { return timeslot; }

    public synchronized boolean bookedWorkoutSlot(String userId){
        if (availableSeats > 0 && !bookedUsers.contains(userId)){
            availableSeats -= 1;
            bookedUsers.add(userId);
            return true;
        }
        return false;
    }

    public synchronized boolean cancelWorkoutSlot(String userId){
        if (bookedUsers.contains(userId)){
            availableSeats += 1;
            bookedUsers.remove(userId);
            return true;
        }
        return false;
    }

    public boolean isUserBooked(String userId){
        return bookedUsers.contains(userId);
    }

    @Override
    public String toString(){
        return "WorkoutSlot{" + 
        "start=" + timeslot.getStartTime() + 
        ", end=" + timeslot.getEndTime() + 
        ", availableSeats = " + availableSeats;
    }
}
