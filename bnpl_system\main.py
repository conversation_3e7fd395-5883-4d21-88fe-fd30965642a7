import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bnpl_system.service.UserService import UserService
from bnpl_system.service.ItemService import ItemService
from bnpl_system.service.OrderService import OrderService

class Main:
    def __init__(self):
        self.item_service = ItemService()
        self.user_service = UserService()
        self.order_service = OrderService(self.user_service, self.item_service)

if __name__ == "__main__":
    main = Main()
        
    # 1. Seed Inventory
    print("\n--- 1. Seeding Inventory ---")
    main.item_service.seed_inventory([
        ("Laptop", 10, 500.0),
        ("Mobile", 20, 100.0),
        ("Headphones", 15, 20.0),
    ])
    main.item_service.view_inventory()
    
    # 2. Register Users
    print("\n--- 2. Registering Users ---")
    user1 = main.user_service.register_user("user1", credit_limit=1000)
    user2 = main.user_service.register_user("user2", credit_limit=2000)
    print(f"Registered: {user1}")
    print(f"Registered: {user2}")
    
    # 3. Place Orders
    print("\n--- 3. Placing Orders ---")
    cart1 = [("Laptop", 1), ("Headphones", 2)]
    order1 = main.order_service.buy("user1", cart1, "BNPL", "2026-01-17")
    
    cart2 = [("Mobile", 2)]
    order2 = main.order_service.buy("user2", cart2, "PREPAID", "2026-01-16")
    
    # 4. View Inventory After Orders
    print("\n--- 4. Inventory After Orders ---")
    main.item_service.view_inventory()
    
    # 5. View Dues
    print("\n--- 5. Viewing Dues ---")
    main.order_service.view_dues("user1", "2026-01-17")
    main.order_service.view_dues("user2", "2026-01-17")

    # 6. Order Status
    print("\n--- 6. Order Status After Purchase ---")
    main.order_service.order_status("user1")
    main.order_service.order_status("user2")

    # 7. Clear Dues
    print("\n--- 7. Clearing Dues ---")
    main.order_service.clear_dues("user1", [order1.order_id], "2026-01-17")
    
    # 8. View Dues After Clearing
    print("\n--- 8. Dues After Clearing ---")
    main.order_service.view_dues("user1", "2026-01-17")
    
    # 9. Order Status
    print("\n--- 9. Order Status After Clearing Dues ---")
    main.order_service.order_status("user1")
    main.order_service.order_status("user2")
    
    # 10. Optional - Add/Remove Inventory
    print("\n--- 10. Modifying Inventory ---")
    main.item_service.add_inventory("Tablet", 5, 300.0)
    main.item_service.remove_inventory("Mobile", 1)
    main.item_service.view_inventory()