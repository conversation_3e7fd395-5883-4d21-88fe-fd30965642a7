#!/usr/bin/env python3
"""
Setup script to create test database and sample data for MySQL diagnostics tool
"""

import mysql.connector
from mysql.connector import Error
import getpass

def create_test_database():
    """Create test database with sample data"""
    
    # Get MySQL credentials
    print("Setting up test database for MySQL Query Diagnostics Tool")
    print("=" * 60)
    
    host = input("MySQL Host (default: localhost): ").strip() or "localhost"
    port = input("MySQL Port (default: 3306): ").strip() or "3306"
    username = input("MySQL Username (default: root): ").strip() or "root"
    password = getpass.getpass("MySQL Password: ")
    
    try:
        port = int(port)
    except ValueError:
        print("Invalid port number. Using default 3306.")
        port = 3306
    
    try:
        # Connect to MySQL server
        print(f"\nConnecting to MySQL at {host}:{port}...")
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=username,
            password=password
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            print("✓ Connected to MySQL server successfully!")
            
            # Create test database
            print("\nCreating test database 'ecommerce_test'...")
            cursor.execute("DROP DATABASE IF EXISTS ecommerce_test")
            cursor.execute("CREATE DATABASE ecommerce_test")
            cursor.execute("USE ecommerce_test")
            print("✓ Database 'ecommerce_test' created successfully!")
            
            # Create tables
            print("\nCreating tables...")
            
            # Users table
            cursor.execute("""
                CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    email VARCHAR(150) UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_email (email),
                    INDEX idx_created_at (created_at)
                )
            """)
            
            # Products table
            cursor.execute("""
                CREATE TABLE products (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    stock_quantity INT DEFAULT 0,
                    INDEX idx_category (category),
                    INDEX idx_price (price)
                )
            """)
            
            # Orders table
            cursor.execute("""
                CREATE TABLE orders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    total_amount DECIMAL(10,2) NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_status (status),
                    INDEX idx_created_at (created_at)
                )
            """)
            
            # Order items table
            cursor.execute("""
                CREATE TABLE order_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    product_id INT NOT NULL,
                    quantity INT NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    FOREIGN KEY (order_id) REFERENCES orders(id),
                    FOREIGN KEY (product_id) REFERENCES products(id),
                    INDEX idx_order_id (order_id),
                    INDEX idx_product_id (product_id)
                )
            """)
            
            print("✓ Tables created successfully!")
            
            # Insert sample data
            print("\nInserting sample data...")
            
            # Insert users
            users_data = [
                ('John Doe', '<EMAIL>'),
                ('Jane Smith', '<EMAIL>'),
                ('Bob Johnson', '<EMAIL>'),
                ('Alice Brown', '<EMAIL>'),
                ('Charlie Wilson', '<EMAIL>'),
                ('Diana Davis', '<EMAIL>'),
                ('Eve Miller', '<EMAIL>'),
                ('Frank Garcia', '<EMAIL>'),
                ('Grace Lee', '<EMAIL>'),
                ('Henry Taylor', '<EMAIL>')
            ]
            
            cursor.executemany(
                "INSERT INTO users (name, email) VALUES (%s, %s)",
                users_data
            )
            
            # Insert products
            products_data = [
                ('iPhone 15', 'Electronics', 999.99, 50),
                ('Samsung Galaxy S24', 'Electronics', 899.99, 30),
                ('MacBook Pro', 'Electronics', 1999.99, 20),
                ('Dell XPS 13', 'Electronics', 1299.99, 25),
                ('Nike Air Max', 'Shoes', 129.99, 100),
                ('Adidas Ultraboost', 'Shoes', 149.99, 80),
                ('Levi\'s Jeans', 'Clothing', 79.99, 150),
                ('H&M T-Shirt', 'Clothing', 19.99, 200),
                ('Coffee Maker', 'Appliances', 89.99, 40),
                ('Blender', 'Appliances', 59.99, 60)
            ]
            
            cursor.executemany(
                "INSERT INTO products (name, category, price, stock_quantity) VALUES (%s, %s, %s, %s)",
                products_data
            )
            
            print("✓ Sample data inserted successfully!")
            
            # Create configuration file
            config_data = {
                "databases": [
                    {
                        "name": "test_database",
                        "host": host,
                        "port": port,
                        "database": "ecommerce_test",
                        "user": username,
                        "password": password
                    }
                ]
            }
            
            import json
            with open('mysql_test_config.json', 'w') as f:
                json.dump(config_data, f, indent=2)
            
            print("✓ Configuration file 'mysql_test_config.json' created!")
            
            print(f"\n{'='*60}")
            print("✅ Test database setup completed successfully!")
            print(f"{'='*60}")
            print(f"Database: ecommerce_test")
            print(f"Tables: users, products, orders, order_items")
            print(f"Sample data: 10 users, 10 products")
            print(f"Config file: mysql_test_config.json")
            print(f"\nYou can now test the diagnostics tool with:")
            print(f"python mysql_diagnostics_cli.py -c mysql_test_config.json -f sample_query.sql")
            
    except Error as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()
            print("\n✓ MySQL connection closed.")
    
    return True

if __name__ == "__main__":
    create_test_database()
