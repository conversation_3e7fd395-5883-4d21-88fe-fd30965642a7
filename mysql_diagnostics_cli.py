#!/usr/bin/env python3
"""
Command Line Interface for MySQL Query Diagnostics Tool
"""

import argparse
import sys
import json
from mysql_query_diagnostics import MySQLQueryDiagnostics, DatabaseConfig

def load_config_from_file(config_file: str) -> dict:
    """Load database configurations from JSON file"""
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading config file: {e}")
        sys.exit(1)

def create_sample_config():
    """Create a sample configuration file"""
    sample_config = {
        "databases": [
            {
                "name": "production",
                "host": "prod-mysql.example.com",
                "port": 3306,
                "database": "ecommerce",
                "user": "readonly_user",
                "password": "secure_password"
            },
            {
                "name": "staging",
                "host": "staging-mysql.example.com",
                "port": 3306,
                "database": "ecommerce",
                "user": "readonly_user",
                "password": "secure_password"
            },
            {
                "name": "development",
                "host": "localhost",
                "port": 3306,
                "database": "ecommerce_dev",
                "user": "dev_user",
                "password": "dev_password"
            }
        ]
    }
    
    with open('mysql_config.json', 'w') as f:
        json.dump(sample_config, f, indent=2)
    
    print("Sample configuration file 'mysql_config.json' created.")
    print("Please update it with your actual database credentials.")

def main():
    parser = argparse.ArgumentParser(description='MySQL Query Diagnostics Tool')
    parser.add_argument('--config', '-c', help='Database configuration file (JSON)')
    parser.add_argument('--query', '-q', help='SQL query to analyze')
    parser.add_argument('--query-file', '-f', help='File containing SQL query')
    parser.add_argument('--databases', '-d', nargs='+', help='Specific databases to analyze (default: all)')
    parser.add_argument('--export', '-e', help='Export results to JSON file')
    parser.add_argument('--compare', action='store_true', help='Compare performance across databases')
    parser.add_argument('--create-sample-config', action='store_true', help='Create sample configuration file')

    args = parser.parse_args()

    if args.create_sample_config:
        create_sample_config()
        return

    # Now require config for other operations
    if not args.config:
        print("Error: --config/-c is required when not using --create-sample-config")
        sys.exit(1)
    
    # Load query
    if args.query:
        query = args.query
    elif args.query_file:
        try:
            with open(args.query_file, 'r') as f:
                query = f.read().strip()
        except Exception as e:
            print(f"Error reading query file: {e}")
            sys.exit(1)
    else:
        print("Please provide either --query or --query-file")
        sys.exit(1)
    
    # Load configuration
    config_data = load_config_from_file(args.config)
    
    # Initialize diagnostics tool
    diagnostics_tool = MySQLQueryDiagnostics()
    
    # Add database configurations
    for db_config in config_data['databases']:
        config = DatabaseConfig(**db_config)
        diagnostics_tool.add_database(config)
    
    try:
        # Run diagnostics
        target_databases = args.databases if args.databases else None
        results = diagnostics_tool.diagnose_across_databases(query, target_databases)
        
        if not results:
            print("No results obtained. Check your database connections and query.")
            sys.exit(1)
        
        # Display results
        for db_name, diagnostics in results.items():
            diagnostics_tool.print_diagnostics_report(diagnostics)
        
        # Compare performance if requested
        if args.compare and len(results) > 1:
            diagnostics_tool.compare_performance(results)
        
        # Export results if requested
        if args.export:
            from dataclasses import asdict
            export_data = {db_name: asdict(diagnostics) for db_name, diagnostics in results.items()}
            with open(args.export, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
            print(f"Results exported to {args.export}")
    
    finally:
        # Clean up connections
        diagnostics_tool.disconnect_all()

if __name__ == "__main__":
    main()
