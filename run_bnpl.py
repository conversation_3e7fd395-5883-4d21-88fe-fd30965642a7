#!/usr/bin/env python3
"""
Standalone BNPL system runner
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

def main():
    print("🚀 Starting BNPL System...")
    
    try:
        # Import all required modules
        from bnpl_system.service.UserService import UserService
        from bnpl_system.service.ItemService import ItemService  
        from bnpl_system.service.OrderService import OrderService
        
        print("✅ All modules imported successfully")
        
        # Initialize services
        item_service = ItemService()
        user_service = UserService()
        order_service = OrderService(user_service, item_service)
        
        print("✅ All services initialized")
        print("\n" + "="*50)
        print("           BNPL SYSTEM DEMO")
        print("="*50)
        
        # 1. Seed Inventory
        print("\n📦 STEP 1: Seeding Inventory")
        item_service.seed_inventory([
            ("Laptop", 10, 500.0),
            ("Mobile", 20, 100.0),
            ("Headphones", 15, 20.0),
        ])
        item_service.view_inventory()
        
        # 2. Register Users
        print("\n👥 STEP 2: Registering Users")
        user1 = user_service.register_user("user1", credit_limit=1000)
        user2 = user_service.register_user("user2", credit_limit=2000)
        print(f"✅ Registered: {user1}")
        print(f"✅ Registered: {user2}")
        
        # 3. Place Orders
        print("\n🛒 STEP 3: Placing Orders")
        cart1 = [("Laptop", 1), ("Headphones", 2)]
        print(f"User1 cart: {cart1}")
        order1 = order_service.place_order("user1", cart1, "BNPL", "2024-01-01")
        
        cart2 = [("Mobile", 2)]
        print(f"User2 cart: {cart2}")
        order2 = order_service.place_order("user2", cart2, "PREPAID", "2024-01-02")
        
        # 4. View Updated Inventory
        print("\n📦 STEP 4: Updated Inventory")
        item_service.view_inventory()
        
        # 5. View Dues
        print("\n💳 STEP 5: Viewing Dues")
        order_service.view_dues("user1", "2024-02-01")
        order_service.view_dues("user2", "2024-02-01")
        
        # 6. Clear Dues
        print("\n💰 STEP 6: Clearing Dues")
        order_service.clear_dues("user1", [order1.order_id], "2024-02-02")
        
        # 7. View Dues After Clearing
        print("\n💳 STEP 7: Dues After Clearing")
        order_service.view_dues("user1", "2024-02-03")
        
        # 8. Order Status
        print("\n📋 STEP 8: Order History")
        order_service.order_status("user1")
        order_service.order_status("user2")
        
        print("\n" + "="*50)
        print("🎉 BNPL SYSTEM DEMO COMPLETED SUCCESSFULLY!")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ The BNPL system is working perfectly!")
    else:
        print("\n💥 There were errors in the BNPL system.")
    
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
