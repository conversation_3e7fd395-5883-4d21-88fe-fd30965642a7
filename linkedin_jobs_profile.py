# linkedin_jobs_profile.py

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time

# Chrome options to use existing Chrome user profile
options = webdriver.ChromeOptions()
options.add_argument("user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")  # Adjust path if needed
options.add_argument("profile-directory=Default")  # Or 'Profile 1', etc.

# Launch Chrome
driver = webdriver.Chrome(options=options)

# Step 1: Open LinkedIn Profile (optional)
driver.get("https://www.linkedin.com/in/ashutoshchatterjee98/")
time.sleep(5)

# Step 2: Navigate to Jobs page
driver.get("https://www.linkedin.com/jobs/")
time.sleep(5)

# Step 3: Search for a job
search_box = driver.find_element(By.CSS_SELECTOR, 'input.jobs-search-box__text-input')
search_box.send_keys("Software Engineer")
time.sleep(2)
search_box.send_keys(Keys.ARROW_DOWN)
search_box.send_keys(Keys.ENTER)
time.sleep(5)

# Step 4: Filter by "Past 24 hours"
filter_button = driver.find_element(By.XPATH, "//button[contains(@aria-label, 'Date posted filter')]")
filter_button.click()
time.sleep(2)

past_24_hours_option = driver.find_element(By.XPATH, "//label[contains(., 'Past 24 hours')]")
past_24_hours_option.click()
time.sleep(1)

apply_button = driver.find_element(By.XPATH, "//button[contains(., 'Show results')]")
apply_button.click()
time.sleep(5)

# Step 5: Modify URL for "Past 1 hour"
current_url = driver.current_url
if 'f_TPR' in current_url:
    new_url = current_url.replace('r86400', 'r3600')
    driver.get(new_url)
else:
    print("URL does not have expected filter. Check if 'Date posted' was applied.")

# Wait before closing
time.sleep(10)
driver.quit()
