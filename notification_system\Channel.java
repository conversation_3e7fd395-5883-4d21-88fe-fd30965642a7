package notification_system;

public class Channel {
    private ChannelType channelType;
    private boolean isActive;

    public Channel(ChannelType channelType) {
        this.channelType = channelType;
        this.isActive = true;
    }
    public ChannelType getChannelType() {
        return channelType;
    }
    public boolean isActive() {
        return isActive;
    }
    public void setActive(boolean active) {
        isActive = active;
    }
    @Override
    public String toString() {
        return "Channel{" + "channelType=" + channelType + ", isActive=" + isActive + '}';
    }
}
