#!/usr/bin/env python3
"""
Generate HTML report from MySQL diagnostics JSON data
"""

import json
import sys
from datetime import datetime

def generate_html_report(json_file, output_file="diagnostics_report.html"):
    """Generate an HTML report from JSON diagnostics data"""
    
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error reading JSON file: {e}")
        return False
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL Query Diagnostics Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
        }}
        .database-section {{
            margin: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }}
        .database-header {{
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }}
        .database-name {{
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin: 0;
        }}
        .execution-time {{
            color: #28a745;
            font-weight: bold;
            margin: 5px 0;
        }}
        .query-section {{
            padding: 20px;
        }}
        .query-box {{
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }}
        .explain-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .explain-table th,
        .explain-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .explain-table th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        .explain-table tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .warnings {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }}
        .warnings h3 {{
            color: #856404;
            margin-top: 0;
        }}
        .warnings ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
        .warnings li {{
            color: #856404;
            margin: 5px 0;
        }}
        .recommendations {{
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }}
        .recommendations h3 {{
            color: #0c5460;
            margin-top: 0;
        }}
        .recommendations ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
        .recommendations li {{
            color: #0c5460;
            margin: 5px 0;
        }}
        .timestamp {{
            color: #6c757d;
            font-size: 0.9em;
            text-align: right;
            margin: 10px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 MySQL Query Diagnostics Report</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
"""
    
    # Process each database
    for db_name, db_data in data.items():
        html_content += f"""
        <div class="database-section">
            <div class="database-header">
                <h2 class="database-name">📊 {db_name.upper()}</h2>
                <div class="execution-time">⚡ Execution Time: {db_data['execution_time']:.4f} seconds</div>
                <div class="timestamp">🕒 {db_data['timestamp']}</div>
            </div>
            
            <div class="query-section">
                <h3>📝 Query</h3>
                <div class="query-box">{db_data['query']}</div>
                
                <h3>📋 EXPLAIN Plan</h3>
                <table class="explain-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Select Type</th>
                            <th>Table</th>
                            <th>Type</th>
                            <th>Key</th>
                            <th>Rows</th>
                            <th>Extra</th>
                        </tr>
                    </thead>
                    <tbody>
"""
        
        # Add EXPLAIN plan rows
        for plan in db_data['explain_plan']:
            html_content += f"""
                        <tr>
                            <td>{plan['id']}</td>
                            <td>{plan['select_type']}</td>
                            <td>{plan['table']}</td>
                            <td>{plan['type']}</td>
                            <td>{plan['key'] or 'None'}</td>
                            <td>{plan['rows']}</td>
                            <td>{plan['extra'] or ''}</td>
                        </tr>
"""
        
        html_content += """
                    </tbody>
                </table>
"""
        
        # Add warnings if any
        if db_data['warnings']:
            html_content += """
                <div class="warnings">
                    <h3>⚠️ Warnings</h3>
                    <ul>
"""
            for warning in db_data['warnings']:
                html_content += f"                        <li>{warning}</li>\n"
            
            html_content += """
                    </ul>
                </div>
"""
        
        # Add recommendations if any
        if db_data['recommendations']:
            html_content += """
                <div class="recommendations">
                    <h3>💡 Recommendations</h3>
                    <ul>
"""
            for rec in db_data['recommendations']:
                html_content += f"                        <li>{rec}</li>\n"
            
            html_content += """
                    </ul>
                </div>
"""
        
        html_content += """
            </div>
        </div>
"""
    
    html_content += """
    </div>
</body>
</html>
"""
    
    # Write HTML file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"✅ HTML report generated: {output_file}")
        return True
    except Exception as e:
        print(f"❌ Error writing HTML file: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python generate_html_report.py <json_file>")
        print("Example: python generate_html_report.py fixed_diagnostics.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    output_file = json_file.replace('.json', '_report.html')
    
    if generate_html_report(json_file, output_file):
        print(f"🌐 Open the report in your browser: {output_file}")
    else:
        sys.exit(1)
