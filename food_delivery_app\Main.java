import java.util.*;

public class Main {
    public static void main(String[] args){
        foodOrderingSystem system = new foodOrderingSystem();

        system.registerCustomer("Alice", "C1");
        system.registerRestaurant("Dominos", "R1");

        system.addItemToRestaurant("R1", "I1", "Chicken Pizza", 10.0, 5);
        system.addItemToRestaurant("R1", "I2", "Veg Pizza", 5.0, 5);

        System.out.println("Search results:");
        List<Item> items = system.searchItems("R1", "pizza" );
        for (Item item: items){
            System.out.println(item);
        }

        Map<String, Integer> orderItems = new HashMap<>();
        orderItems.put("I1", 2);
        orderItems.put("I2", 1);
        system.placeOrder("C1", "R1", orderItems);

        System.out.println("\nCustomer Orders: ");
        List<Order> userOrders = system.getOrders("C1");
        for (Order order : userOrders){
            System.out.println(order);
        }

        String orderId = system.getOrders("C1").get(0).getOrderId();
        system.cancelOrder("C1", orderId);

        System.out.println("\nCustomer orders after cancellation");
        List<Order> orders = system.getOrders("C1");
        for (Order order : orders){
            System.out.println(order);
        }
    }
}
