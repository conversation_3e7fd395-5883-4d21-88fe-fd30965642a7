package clearfit;
import java.util.*;

public class Center {
    private String centername;
    private List<TimeSlot> timings;
    private List<String> workoutType;
    private Map<String, List<WorkoutSlot>> workoutSchedule;

    public Center(String centername, List<TimeSlot> timings, 
    List<String> workoutType, Map<String, List<WorkoutSlot>> workoutSchedule){
        this.centername = centername;
        this.timings = timings;
        this.workoutType = workoutType;
        this.workoutSchedule = workoutSchedule;
    }

    public String getCentername() { return centername; }
    public List<TimeSlot> getTimings() { return timings; }
    public List<String> getWorkoutType() { return workoutType; }
    public Map<String, List<WorkoutSlot>> getWorkoutSchedule() { return workoutSchedule; }
    
}
