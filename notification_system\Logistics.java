package notification_system;

public class Logistics extends User {
    private int logisticsId;
    public Logistics(int userId, String userName, String email, String phoneNumber, int logisticsId) {
        super(userId, userName, email, phoneNumber);
        this.logisticsId = logisticsId;
    }
    
    public int getLogisticsId() {
        return logisticsId;
    }

    //logistics will only get notified when an order is and shipped.
    public String notifyLogistics(int userId, int orderId, Event event, ChannelType channel) {
        if (event.getEventType() == EventType.SHIPPED) {
            return "Logistics " + userId + " notified for order " + orderId + " via " + channel.toString();
        }
        return "Logistics " + userId + " not notified for order " + orderId + " via " + channel.toString();
    }
}
