public class Item{
    private String id;
    private String name;
    private double price;
    private int quantity;

    public Item(String id, String name, double price, int quantity){
        this.id = id;
        this.name = name;
        this.price = price;
        this.quantity = quantity;
    }

    public String getId() { return id; }
    public String getName() { return name; }
    public double getPrice() { return price; }
    public int getQuantity() { return quantity; }

    public void reduceQuantity(int q){
        if (q <= quantity) quantity -= q;
        else throw new IllegalArgumentException("Not enough quantity available.");
    }

    @Override
    public String toString(){
        return String.format("%s (%s): $%.2f, Qty: %d", name, id, price, quantity);
    }
}