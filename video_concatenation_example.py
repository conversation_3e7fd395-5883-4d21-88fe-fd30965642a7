
"""
Example usage of the video concatenation functionality.

This script demonstrates different ways to use the video concatenation features.
"""

from movies import concatenate_videos, get_video_files
import os

def example_basic_concatenation():
    """Example: Basic video concatenation with a list of files."""
    print("Example 1: Basic concatenation")
    print("-" * 30)
    
    # List of video files to concatenate (replace with your actual file paths)
    video_list = [
        "video1.mp4",
        "video2.mp4", 
        "video3.mp4"
    ]
    
    output_path = "combined_video.mp4"
    
    # Check if files exist before attempting concatenation
    existing_files = [f for f in video_list if os.path.exists(f)]
    
    if existing_files:
        success = concatenate_videos(existing_files, output_path)
        if success:
            print(f"✅ Successfully created: {output_path}")
        else:
            print("❌ Concatenation failed")
    else:
        print("⚠️  No video files found. Please update the video_list with actual file paths.")

def example_directory_concatenation():
    """Example: Concatenate all videos from a directory."""
    print("\nExample 2: Directory concatenation")
    print("-" * 35)
    
    directory = "sample_videos"  # Replace with your directory path
    
    if os.path.isdir(directory):
        video_files = get_video_files(directory)
        
        if video_files:
            print(f"Found {len(video_files)} videos in '{directory}':")
            for i, file in enumerate(video_files, 1):
                print(f"  {i}. {os.path.basename(file)}")
            
            output_path = os.path.join(directory, "all_videos_combined.mp4")
            success = concatenate_videos(video_files, output_path)
            
            if success:
                print(f"✅ Successfully created: {output_path}")
            else:
                print("❌ Concatenation failed")
        else:
            print(f"No video files found in '{directory}'")
    else:
        print(f"⚠️  Directory '{directory}' does not exist. Please create it and add some video files.")

def example_custom_concatenation():
    """Example: Custom concatenation with specific settings."""
    print("\nExample 3: Custom concatenation")
    print("-" * 32)
    
    # Custom list of videos with specific order
    video_list = [
        "intro.mp4",
        "main_content.mp4",
        "outro.mp4"
    ]
    
    output_path = "final_video.mp4"
    
    # Check if files exist
    existing_files = [f for f in video_list if os.path.exists(f)]
    
    if existing_files:
        # Use custom settings
        success = concatenate_videos(
            video_paths=existing_files,
            output_path=output_path,
            method="compose",  # or "chain" for different concatenation method
            verbose=True
        )
        
        if success:
            print(f"✅ Successfully created: {output_path}")
        else:
            print("❌ Concatenation failed")
    else:
        print("⚠️  No video files found. Please update the video_list with actual file paths.")

def create_sample_structure():
    """Helper function to create a sample directory structure."""
    print("\nCreating sample directory structure...")
    print("-" * 40)
    
    sample_dir = "sample_videos"
    os.makedirs(sample_dir, exist_ok=True)
    
    print(f"📁 Created directory: {sample_dir}")
    print("💡 Add your video files to this directory and run the examples again!")
    
    # Create a simple text file with instructions
    instructions_file = os.path.join(sample_dir, "README.txt")
    with open(instructions_file, 'w') as f:
        f.write("Add your video files (.mp4, .avi, .mov, etc.) to this directory.\n")
        f.write("Then run the video_concatenation_example.py script to see the examples in action!\n")
    
    print(f"📄 Created instructions: {instructions_file}")

if __name__ == "__main__":
    print("🎬 Video Concatenation Examples")
    print("=" * 50)
    
    # Run examples
    example_basic_concatenation()
    example_directory_concatenation()
    example_custom_concatenation()
    
    # Create sample structure if needed
    create_sample_structure()
    
    print("\n" + "=" * 50)
    print("💡 Tips:")
    print("1. Make sure moviepy is installed: pip install moviepy")
    print("2. Update file paths in the examples to match your actual video files")
    print("3. Supported formats: MP4, AVI, MOV, MKV, WMV, FLV, WEBM")
    print("4. For large files, concatenation may take some time")
    print("5. Use the main movies.py script for interactive usage")

