-- PostgreSQL Database Setup Script
-- This script creates a users table with indexes and populates it with sample data

-- Create the users table
CREATE TABLE users (
    id SERIAL,
    first TEXT,
    last TEXT,
    birth_year INTEGER,
    PRIMARY KEY (id)
);

-- Create secondary indexes
CREATE INDEX ix_users_first_last ON users (first, last);
CREATE INDEX ix_users_birth_year ON users (birth_year);

-- Insert sample data
INSERT INTO users (first, last, birth_year) VALUES
    ('<PERSON>', '<PERSON>', 1985),
    ('<PERSON>', '<PERSON><PERSON>', 1990),
    ('<PERSON>', '<PERSON>', 1978),
    ('<PERSON>', '<PERSON>', 1992),
    ('<PERSON>', '<PERSON>', 1983),
    ('<PERSON>', '<PERSON>', 1987),
    ('<PERSON>', '<PERSON>', 1975),
    ('<PERSON>', '<PERSON>', 1989),
    ('<PERSON>', '<PERSON>', 1981),
    ('<PERSON>', '<PERSON>', 1994),
    ('<PERSON>', '<PERSON>', 1976),
    ('<PERSON>', '<PERSON>', 1988),
    ('<PERSON>', '<PERSON>', 1982),
    ('<PERSON>', '<PERSON>', 1979),
    ('<PERSON>', '<PERSON>', 1991),
    ('<PERSON>', '<PERSON>', 1986),
    ('<PERSON>', '<PERSON>', 1984),
    ('<PERSON>', '<PERSON>', 1993),
    ('<PERSON>', '<PERSON>', 1977),
    ('<PERSON>', '<PERSON>', 1980),
    ('<PERSON>', '<PERSON>', 1995),
    ('<PERSON>', '<PERSON>', 1974),
    ('<PERSON>', '<PERSON>', 1996),
    ('<PERSON>', '<PERSON>', 1973),
    ('<PERSON>', '<PERSON>', 1997),
    ('<PERSON>', '<PERSON>', 1972),
    ('<PERSON>', '<PERSON>', 1998),
    ('<PERSON>', '<PERSON>', 1971),
    ('<PERSON>', '<PERSON>', 1999),
    ('<PERSON>', 'Hall', 1970);

-- Display table information
SELECT 'Table created successfully!' as status;

-- Show sample of inserted data
SELECT 'Sample data:' as info;
SELECT * FROM users LIMIT 10;

-- Show index information
SELECT 'Indexes created:' as info;
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'users';

-- Show table statistics
SELECT 'Table statistics:' as info;
SELECT 
    COUNT(*) as total_records,
    MIN(birth_year) as earliest_birth_year,
    MAX(birth_year) as latest_birth_year,
    COUNT(DISTINCT first) as unique_first_names,
    COUNT(DISTINCT last) as unique_last_names
FROM users;
