export type ChannelType = 'email' | 'sms' | 'app';

export interface NotificationChannel {
    type: ChannelType;
    send(userId: string, message: string): void;
}

export class EmailChannel implements NotificationChannel {
    type: ChannelType = 'email';
    send(userId: string, message: string) {
        console.log(`[EMAIL] To ${userId}: ${message}`);
    }
}

export class SMSChannel implements NotificationChannel {
    type: ChannelType = 'sms';
    send(userId: string, message: string) {
        console.log(`[SMS] To ${userId}: ${message}`);
    }
}

export class AppChannel implements NotificationChannel {
    type: ChannelType = 'app';
    send(userId: string, message: string) {
        console.log(`[APP] To ${userId}: ${message}`);
    }
}
