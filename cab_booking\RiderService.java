package cab_booking;
import java.util.*;

public class RiderService {
    private Map<String, Rider> riderMap;
    private DriverService driverService;
    private Map<String, Ride> rideMap; 
    
    public RiderService(Map<String, Rider> riderMap, DriverService driverService, Map<String, Ride> rideMap){
        this.riderMap = riderMap;
        this.driverService = driverService;
        this.rideMap = rideMap;
    }

    public Map<String, Rider> getRiderMap() {return riderMap; }

    public String addRider(String name, String riderId, Location location){
        Rider rider = new Rider(name, riderId, location);
        if (riderMap.containsKey(riderId)){
            throw new IllegalArgumentException("Rider already exists: " + riderId);
        }
        riderMap.put(riderId, rider);
        return "Rider: " + riderId + " successfully registered.";
    }

    public String requestRide(String riderId, Location location){
        if (!riderMap.containsKey(riderId)){
            throw new IllegalArgumentException("Rider " + riderId + "does not exist in system.");
        }
        PriorityQueue<Pair> availableDrivers = new PriorityQueue<>(Comparator.comparingInt(Pair::getDistance));
        Rider rider = riderMap.get(riderId);
        Location riderLocation = rider.getLocation();
        int x1 = riderLocation.x, y1 = riderLocation.y;
        Map<String, Driver> allDrivers = driverService.getDriverMap();
        for (Driver driver : allDrivers.values()){
            if (driver.getIsAvailable()){
                Location driverLocation = driver.getLocation();
                int x2 = driverLocation.x, y2 = driverLocation.y;
                int distance = Math.abs(x1 - x2) + Math.abs(y1 - y2);
                availableDrivers.add(new Pair(distance, driver.getDriverId()));
            }
        }

        if (availableDrivers.isEmpty()){
            return "No Drivers available currently.";
        }

        Pair availableDriver = availableDrivers.poll();
        Driver closestDriver = allDrivers.get(availableDriver.getId());
        closestDriver.setIsAvailable(false);
        String rideId = UUID.randomUUID().toString();
        Ride ride = new Ride(rideId, rider, closestDriver, rider.getLocation());
        rideMap.put(rideId, ride);
        return "Ride for riderId: " + riderId + " started with driverId: " + closestDriver.getDriverId() + " (Ride ID: " + rideId + ")";
    }

     public String endTrip(String rideId, Location endLocation) {
        if (!rideMap.containsKey(rideId)) {
            throw new IllegalArgumentException("Ride not found: " + rideId);
        }

        Ride ride = rideMap.get(rideId);
        if (ride.getStatus() == RideStatus.COMPLETED) {
            return "Ride already completed.";
        }

        ride.endRide(endLocation); // marks ride as completed + driver available

        return "Ride completed successfully. Ride ID: " + rideId +
               ", Driver: " + ride.getDriver().getDriverId() +
               ", Status: " + ride.getStatus();
    }

}
