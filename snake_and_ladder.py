import random


class Dice:
    def roll(self):
        return random.randint(1, 6)

class Board:
    def __init__(self, size = 100):
        self.size = size
        self.cells = [i for i in range(1, size + 1)]
        self.snakes = []
        self.ladders = []

    def add_snake(self, snake):
        self.snakes.append(snake)

    def add_ladder(self, ladder):
        self.ladders.append(ladder)

    def check_snake(self, position):
        for snake in self.snakes:
            if snake.snake_start == position:
                return snake.snake_end
        return position
    
    def check_ladder(self, position):
        for ladder in self.ladders:
            if ladder.ladder_start == position:
                return ladder.ladder_end
        return position

class Player:
    def __init__(self, name):
        self.name = name
        self.position = 1
    
    def move(self, steps):
        self.position += steps

class Snake:
    def __init__(self, snake_start, snake_end):
        if snake_start > snake_end:
            self.snake_start = snake_start
            self.snake_end = snake_end
        else:
            raise ValueError("Snake start position must be greater than end position.")

class Ladder:
    def __init__(self, ladder_start, ladder_end):
        if ladder_start < ladder_end:
            self.ladder_start = ladder_start
            self.ladder_end = ladder_end
        else:
            raise ValueError("Ladder start position must be smaller than end position.")

class Game:
    def __init__(self, players, board, dice):
        self.players = players
        self.board = board
        self.dice = dice

    def play_turn(self, player):
        roll = self.dice.roll()
        print(f"{player.name} has rolled {roll}.")
        player.move(roll)

        new_position = self.board.check_snake(player.position)
        if new_position != player.position:
            print(f"Oh no! {player.name} encountered a snake and moved from {player.position} to {new_position}")
            player.position = new_position

        new_position = self.board.check_ladder(player.position)
        if new_position != player.position:
            print(f"Oh Yes! {player.name} encountered a ladder and moved from {player.position} to {new_position}")
            player.position = new_position
        
        if player.position > self.board.size:
            player.position = self.board.size
        
        print(f"{player.name} is now at position {player.position}.")

    def start(self):
        while True:
            for player in self.players:
                self.play_turn(player)

                if player.position == self.board.size:
                    print(f"{player.name} wins!")
                    return
                
board = Board()
snake = Snake(34, 2)
board.add_snake(snake)
snake = Snake(32, 10)
board.add_snake(snake)
snake = Snake(62, 18)
board.add_snake(snake)
snake = Snake(48, 26)
board.add_snake(snake)
snake = Snake(88, 24)
board.add_snake(snake)
snake = Snake(95, 56)
board.add_snake(snake)
snake = Snake(97, 78)
board.add_snake(snake)
ladder = Ladder(4, 14)
board.add_ladder(ladder)
ladder = Ladder(8, 10)
board.add_ladder(ladder)
ladder = Ladder(21, 42)
board.add_ladder(ladder)
ladder = Ladder(28, 76)
board.add_ladder(ladder)
ladder = Ladder(50, 67)
board.add_ladder(ladder)
ladder = Ladder(71, 92)
board.add_ladder(ladder)
ladder = Ladder(88, 99)
board.add_ladder(ladder)

players = [Player("Alice"), Player("Bob")]

dice = Dice()

game = Game(players, board, dice)
game.start()