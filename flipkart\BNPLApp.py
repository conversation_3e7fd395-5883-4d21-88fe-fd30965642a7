from datetime import date
from typing import Dict, List, Tuple
from models.Item import Item
from models.User import User
from PaymentMethod import PaymentMethod
from models.Order import Order

class BNPLApp:
    def __init__(self):
        self.inventory: Dict[str, Item] = {}
        self.users: Dict[str, User] = {}
        self._next_order_id: int = 1

    def seed_inventory(self, items: List[Tuple[str, int, float]]) -> None:
        for name, count, price in items:
            self.inventory[name] = Item(name=name, price=price, count=count)

    def view_inventory(self) -> None:
        for item in self.inventory.values():
            print(f"{item.name}: price={item.price}, stock={item.count}")

    def register_user(self, user_id: str, credit_limit: float) -> None:
        if user_id in self.users:
            raise ValueError("User already exists")
        self.users[user_id] = User(
            user_id=user_id,
            credit_limit=credit_limit,
            remaining_limit=credit_limit
        )
        print(f"\nUser {user_id} registered successfully")

    def _get_user(self, user_id: str) -> User:
        if user_id not in self.users:
            raise ValueError("User not registered")
        return self.users[user_id]

    def _blacklist(self):
        # will be implemented later
        pass

    def buy(self, user_id: str, cart: List[Tuple[str, int]],
             payment_method: PaymentMethod, purchase_date: date) -> int:
        
        try:
            user = self._get_user(user_id)

            for name, qty in cart:
                if name not in self.inventory:
                    raise ValueError(f"Item {name} not found")
                if self.inventory[name].count < qty:
                    raise ValueError(f"Insufficient stock for {name}")

            total = sum(self.inventory[name].price * qty for name, qty in cart)

            if payment_method == PaymentMethod.BNPL:
                if total > user.remaining_limit:
                    raise ValueError("Not enough BNPL credit")
                user.remaining_limit -= total
            elif payment_method == PaymentMethod.PREPAID:
                pass  # assume prepaid payments always succeed

            for name, qty in cart:
                self.inventory[name].count -= qty

            order = Order(
                order_id=self._next_order_id,
                user_id=user_id,
                items=cart,
                total_amount=total,
                payment_method=payment_method,
                purchase_date=purchase_date
            )
            user.orders.append(order)
            self._next_order_id += 1

            print(f"\nOrder placed successfully. ID = {order.order_id}, Amount = {total}, By = {user_id}")
            return order.order_id
        
        except Exception as e:
            return {"success": False, "message": str(e)}

    def clear_dues(self, user_id: str, order_ids: List[int], clearing_date: date) -> None:
        user = self._get_user(user_id)

        for oid in order_ids:
            orders = {o.order_id: o for o in user.orders}
            order = orders.get(oid)
            if not order:
                print(f"\nOrder {oid} not found")
                continue
            if order.cleared or order.payment_method != PaymentMethod.BNPL:
                print(f"No BNPL dues for Order {oid}")
                continue

            order.cleared = True
            order.cleared_date = clearing_date
            user.remaining_limit += order.total_amount
            print(f"\nDues cleared for order {oid}")


    def view_dues(self, user_id: str, upto_date: date) -> None:
        user = self._get_user(user_id)
        dues = [o for o in user.orders if o.is_due(upto_date)]
        dues.sort(key=lambda o: o.purchase_date)

        print(f"\nDUES for {user_id} upto {upto_date}")
        if not dues:
            print("\nNo dues.")
            return

        for o in dues:
            print(
                f"OrderID={o.order_id} | Amount={o.total_amount} | "
                f"Date={o.purchase_date} | Status={o.status_on(upto_date)}"
            )


    def order_status(self, user_id: str) -> None:
        user = self._get_user(user_id)
        print(f"\nORDER HISTORY for {user_id}")

        for o in sorted(user.orders, key=lambda x: x.purchase_date):
            print(
                f"OrderID={o.order_id} | Date={o.purchase_date} | "
                f"Payment={o.payment_method} | Amount={o.total_amount} | Cleared={o.cleared}"
            )

        print(f"Current BNPL Credit Left: {user.remaining_limit}")

        