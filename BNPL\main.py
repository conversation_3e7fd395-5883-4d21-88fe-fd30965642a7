from BNPL.service.UserService import UserService
from BNPL.service.ItemService import ItemService
from BNPL.service.OrderService import OrderService

class Main:
    def __init__(self):
        self.item_service = ItemService()
        self.user_service = UserService()
        self.order_service = OrderService(self.user_service, self.item_service)

if __name__ == "__main__":
    main = Main()

    print("=== BNPL System Test ===")

    # 1. Seed Inventory
    print("\n--- 1. Seeding Inventory ---")
    main.item_service.seed_inventory([
        ("Laptop", 10, 500.0),
        ("Mobile", 20, 100.0),
        ("Headphones", 15, 20.0),
    ])
    main.item_service.view_inventory()

    # 2. Register Users
    print("\n--- 2. Registering Users ---")
    user1 = main.user_service.register_user("user1", credit_limit=1000)
    user2 = main.user_service.register_user("user2", credit_limit=2000)
    print(f"Registered: {user1}")
    print(f"Registered: {user2}")

    # 3. Place Orders
    print("\n--- 3. Placing Orders ---")
    cart1 = [("Laptop", 1), ("Headphones", 2)]
    order1 = main.order_service.place_order("user1", cart1, "BNPL", "2024-01-01")

    cart2 = [("Mobile", 2)]
    order2 = main.order_service.place_order("user2", cart2, "PREPAID", "2024-01-02")

    # 4. View Inventory After Orders
    print("\n--- 4. Inventory After Orders ---")
    main.item_service.view_inventory()

    # 5. View Dues
    print("\n--- 5. Viewing Dues ---")
    main.order_service.view_dues("user1", "2024-02-01")
    main.order_service.view_dues("user2", "2024-02-01")

    # 6. Clear Dues
    print("\n--- 6. Clearing Dues ---")
    main.order_service.clear_dues("user1", [order1.order_id], "2024-02-02")

    # 7. View Dues After Clearing
    print("\n--- 7. Dues After Clearing ---")
    main.order_service.view_dues("user1", "2024-02-03")

    # 8. Order Status
    print("\n--- 8. Order Status ---")
    main.order_service.order_status("user1")
    main.order_service.order_status("user2")

    print("\n=== Test Completed Successfully ===")
    print("🎉 All BNPL system components are working correctly!")