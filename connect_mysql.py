#!/usr/bin/env python3
"""
Connect to MySQL and create test database
"""

import mysql.connector
from mysql.connector import Error
import json
import sys

def connect_and_setup():
    """Connect to MySQL and set up test database"""
    
    print("MySQL Query Diagnostics Tool - Database Setup")
    print("=" * 50)
    print("MySQL service is running. Please provide connection details:")
    
    # Get password from user
    password = input("Enter MySQL root password (press Enter if no password): ")
    
    try:
        print("\nConnecting to MySQL...")
        connection = mysql.connector.connect(
            host="localhost",
            port=3306,
            user="root",
            password=password,
            connect_timeout=10
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Get MySQL info
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"✅ Connected to MySQL {version}")
            
            # Create test database
            print("\nSetting up test database...")
            cursor.execute("DROP DATABASE IF EXISTS ecommerce_test")
            cursor.execute("CREATE DATABASE ecommerce_test")
            cursor.execute("USE ecommerce_test")
            
            # Create test table with some complexity for testing
            cursor.execute("""
                CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    email VARCHAR(150) NOT NULL,
                    age INT,
                    city VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_email (email),
                    INDEX idx_city (city),
                    INDEX idx_age (age)
                )
            """)
            
            cursor.execute("""
                CREATE TABLE orders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    amount DECIMAL(10,2),
                    status VARCHAR(20) DEFAULT 'pending',
                    order_date DATE,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_status (status),
                    INDEX idx_order_date (order_date)
                )
            """)
            
            # Insert test data
            users_data = [
                ('John Doe', '<EMAIL>', 25, 'New York'),
                ('Jane Smith', '<EMAIL>', 30, 'Los Angeles'),
                ('Bob Johnson', '<EMAIL>', 35, 'Chicago'),
                ('Alice Brown', '<EMAIL>', 28, 'Houston'),
                ('Charlie Wilson', '<EMAIL>', 32, 'Phoenix'),
                ('Diana Davis', '<EMAIL>', 27, 'Philadelphia'),
                ('Eve Miller', '<EMAIL>', 29, 'San Antonio'),
                ('Frank Garcia', '<EMAIL>', 31, 'San Diego'),
                ('Grace Lee', '<EMAIL>', 26, 'Dallas'),
                ('Henry Taylor', '<EMAIL>', 33, 'San Jose')
            ]
            
            cursor.executemany(
                "INSERT INTO users (name, email, age, city) VALUES (%s, %s, %s, %s)",
                users_data
            )
            
            # Insert orders
            orders_data = [
                (1, 150.00, 'completed', '2024-01-15'),
                (2, 200.50, 'completed', '2024-01-16'),
                (3, 75.25, 'pending', '2024-01-17'),
                (1, 300.00, 'completed', '2024-01-18'),
                (4, 125.75, 'completed', '2024-01-19'),
                (5, 89.99, 'pending', '2024-01-20'),
                (2, 450.00, 'completed', '2024-01-21'),
                (6, 199.99, 'completed', '2024-01-22'),
                (7, 75.50, 'pending', '2024-01-23'),
                (8, 320.25, 'completed', '2024-01-24')
            ]
            
            cursor.executemany(
                "INSERT INTO orders (user_id, amount, status, order_date) VALUES (%s, %s, %s, %s)",
                orders_data
            )
            
            print("✅ Test database created with sample data!")
            
            # Create config file
            config = {
                "databases": [
                    {
                        "name": "test_database",
                        "host": "localhost",
                        "port": 3306,
                        "database": "ecommerce_test",
                        "user": "root",
                        "password": password
                    }
                ]
            }
            
            with open('mysql_test_config.json', 'w') as f:
                json.dump(config, f, indent=2)
            
            print("✅ Configuration file created: mysql_test_config.json")
            
            # Show what was created
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM orders")
            order_count = cursor.fetchone()[0]
            
            print(f"\n{'='*50}")
            print("🎉 Setup Complete!")
            print(f"{'='*50}")
            print(f"Database: ecommerce_test")
            print(f"Tables: users ({user_count} records), orders ({order_count} records)")
            print(f"Config: mysql_test_config.json")
            print(f"\nReady to test! Run:")
            print(f"python mysql_diagnostics_cli.py -c mysql_test_config.json -q \"SELECT * FROM users WHERE email LIKE '%@gmail.com'\"")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"❌ MySQL Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    if not connect_and_setup():
        print("\nSetup failed. Please check your MySQL installation and credentials.")
        sys.exit(1)
