#!/usr/bin/env python3

# Test basic imports
try:
    from service.UserService import UserService
    print("✅ UserService import successful")
except ImportError as e:
    print(f"❌ UserService import failed: {e}")

try:
    from service.ItemService import ItemService
    print("✅ ItemService import successful")
except ImportError as e:
    print(f"❌ ItemService import failed: {e}")

try:
    from service.OrderService import OrderService
    print("✅ OrderService import successful")
except ImportError as e:
    print(f"❌ OrderService import failed: {e}")

try:
    from models.User import User
    print("✅ User model import successful")
except ImportError as e:
    print(f"❌ User model import failed: {e}")

try:
    from models.Item import Item
    print("✅ Item model import successful")
except ImportError as e:
    print(f"❌ Item model import failed: {e}")

try:
    from models.Order import Order
    print("✅ Order model import successful")
except ImportError as e:
    print(f"❌ Order model import failed: {e}")

print("\n🧪 Testing basic instantiation:")
try:
    user_service = UserService()
    print("✅ UserService instantiation successful")
except Exception as e:
    print(f"❌ UserService instantiation failed: {e}")

try:
    item_service = ItemService({})
    print("✅ ItemService instantiation successful")
except Exception as e:
    print(f"❌ ItemService instantiation failed: {e}")
