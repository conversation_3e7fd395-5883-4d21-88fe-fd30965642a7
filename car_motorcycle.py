class Vehicle:
    def __init__(self, make, model, year):
        self.make = make
        self.model = model
        self.year = year
    
    def display_info(self):
        print(f"Vehicle info: {self.year} {self.make} {self.model}")

class Car(Vehicle): # means car inherits vehicle
    def __init__(self, make, model, year, num_doors):
        super().__init__(make, model, year)
        self.num_doors = num_doors
    
    def display_info(self):
        super().display_info()
        print(f"Number of door: {self.num_doors}")

class Motorcycle(Vehicle): # means motorcycle inherits vehicle
    def __init__(self, make, model, year, engine_capacity):
        super().__init__(make, model, year)
        self.engine_capacity = engine_capacity
    
    def display_info(self):
        super().display_info()
        print(f"Engine Capacity: {self.engine_capacity}")

car = Car("Tata", "Nexon", 2024, 4)
motorcycle = Motorcycle("Honda", "Activa", 2021, 340)

car.display_info()
motorcycle.display_info()
