"""
Video Concatenation Tool using OpenCV

This script provides an alternative to moviepy for concatenating videos using OpenCV.
OpenCV is often more reliable and easier to install.

Requirements:
- opencv-python: pip install opencv-python

Usage:
1. Run script and enter directory path when prompted
2. Pass directory as command line argument: python video_concat_opencv.py /path/to/videos
3. Use the concatenate_videos function directly in your code
"""

import cv2
import os
import sys
import glob
import numpy as np

def get_video_info(video_path):
    """Get video information (width, height, fps, frame count)."""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return None
    
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    cap.release()
    return {
        'width': width,
        'height': height,
        'fps': fps,
        'frame_count': frame_count,
        'duration': frame_count / fps if fps > 0 else 0
    }

def concatenate_videos_opencv(video_paths, output_path, target_fps=30, verbose=True):
    """
    Concatenate multiple video files using OpenCV.
    
    Args:
        video_paths (list): List of paths to video files to concatenate
        output_path (str): Path for the output video file
        target_fps (int): Target FPS for output video
        verbose (bool): Whether to print progress information
    
    Returns:
        bool: True if successful, False otherwise
    """
    if not video_paths:
        print("Error: No video files provided")
        return False
    
    if verbose:
        print(f"Concatenating {len(video_paths)} videos using OpenCV...")
        for i, path in enumerate(video_paths, 1):
            print(f"  {i}. {os.path.basename(path)}")
    
    try:
        # Get info from first video to determine output format
        first_video_info = get_video_info(video_paths[0])
        if not first_video_info:
            print(f"Error: Cannot read first video: {video_paths[0]}")
            return False
        
        # Use the dimensions of the first video as reference
        target_width = first_video_info['width']
        target_height = first_video_info['height']
        
        if verbose:
            print(f"Target resolution: {target_width}x{target_height}")
            print(f"Target FPS: {target_fps}")
        
        # Define the codec and create VideoWriter object
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, target_fps, (target_width, target_height))
        
        if not out.isOpened():
            print("Error: Could not open output video writer")
            return False
        
        total_frames_written = 0
        
        # Process each video
        for i, video_path in enumerate(video_paths):
            if verbose:
                print(f"Processing video {i+1}/{len(video_paths)}: {os.path.basename(video_path)}")
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"Warning: Could not open video: {video_path}")
                continue
            
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Resize frame to target dimensions if necessary
                if frame.shape[1] != target_width or frame.shape[0] != target_height:
                    frame = cv2.resize(frame, (target_width, target_height))
                
                out.write(frame)
                frame_count += 1
                total_frames_written += 1
                
                # Show progress for large videos
                if verbose and frame_count % 100 == 0:
                    print(f"  Processed {frame_count} frames...", end='\r')
            
            cap.release()
            if verbose:
                print(f"  ✅ Processed {frame_count} frames from {os.path.basename(video_path)}")
        
        out.release()
        
        if verbose:
            duration = total_frames_written / target_fps
            print(f"✅ Video concatenation completed!")
            print(f"   Total frames: {total_frames_written}")
            print(f"   Duration: {duration:.2f} seconds")
            print(f"   Output: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during video concatenation: {str(e)}")
        return False

def get_video_files(directory, extensions=None):
    """Get all video files from a directory."""
    if extensions is None:
        extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.wmv', '*.flv']
    
    video_files = []
    for ext in extensions:
        video_files.extend(glob.glob(os.path.join(directory, ext)))
        video_files.extend(glob.glob(os.path.join(directory, ext.upper())))
    
    return sorted(video_files)

def main():
    """Main function to handle command line usage."""
    print("🎬 Video Concatenation Tool (OpenCV)")
    print("=" * 45)
    
    # Get directory path from user input or command line argument
    if len(sys.argv) > 1:
        video_dir = sys.argv[1]
    else:
        video_dir = input("Enter the directory containing videos: ").strip()
    
    # Validate directory
    if not os.path.isdir(video_dir):
        print(f"❌ Error: Directory '{video_dir}' does not exist.")
        sys.exit(1)
    
    # Find video files
    video_files = get_video_files(video_dir)
    
    if not video_files:
        print("❌ No video files found in the specified directory.")
        print("Supported formats: MP4, AVI, MOV, MKV, WMV, FLV")
        sys.exit(1)
    
    print(f"\n📁 Found {len(video_files)} video files:")
    for i, file in enumerate(video_files, 1):
        info = get_video_info(file)
        if info:
            print(f"  {i}. {os.path.basename(file)} ({info['width']}x{info['height']}, {info['duration']:.1f}s)")
        else:
            print(f"  {i}. {os.path.basename(file)} (could not read info)")
    
    # Get output filename
    output_filename = input(f"\nEnter output filename (default: concatenated_video.mp4): ").strip()
    if not output_filename:
        output_filename = "concatenated_video.mp4"
    
    # Ensure output has proper extension
    if not output_filename.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
        output_filename += '.mp4'
    
    output_file = os.path.join(video_dir, output_filename)
    
    # Check if output file already exists
    if os.path.exists(output_file):
        overwrite = input(f"⚠️  Output file '{output_filename}' already exists. Overwrite? (y/N): ").strip().lower()
        if overwrite != 'y':
            print("Operation cancelled.")
            sys.exit(0)
    
    # Get target FPS
    fps_input = input("Enter target FPS (default: 30): ").strip()
    try:
        target_fps = int(fps_input) if fps_input else 30
    except ValueError:
        target_fps = 30
    
    # Concatenate videos
    success = concatenate_videos_opencv(video_files, output_file, target_fps)
    
    if success:
        print(f"\n🎉 Success! Concatenated video saved as: {output_filename}")
    else:
        print("\n❌ Failed to concatenate videos.")
        sys.exit(1)

if __name__ == "__main__":
    main()
