import datetime

class Car:
    def __init__(self, car_type, entry_time):
        self.car_type = car_type.upper()  # Ensure case-insensitive type
        self.entry_time = entry_time
        self.exit_time = None
        self.parking_fee = 0

    def calculate_fee(self, current_time):
        if self.exit_time is None:
            self.exit_time = current_time  # Calculate fee upon exit
            parking_duration = (self.exit_time - self.entry_time).total_seconds() / 3600  # Hours
            if self.car_type == "SUV":
                self.parking_fee = parking_duration * 20
            elif self.car_type == "HATCHBACK":
                self.parking_fee = parking_duration * 10
            else:
                print(f"Invalid car type: {self.car_type}")

class ParkingManager:
    def __init__(self):
        self.parked_cars = []
        self.available_hatchback_spaces = 0  # Track specific space availability
        self.available_suv_spaces = 0

    def park_car(self, car_type, current_time):
        car = Car(car_type, current_time)
        self.parked_cars.append(car)

        if car_type == "SUV":
            if self.available_suv_spaces > 0:
                self.available_suv_spaces -= 1
            else:
                print("No available SUV spaces. Car parked in hatchback space (hatchback rate applies).")
        elif car_type == "HATCHBACK":
            if self.available_hatchback_spaces > 0:
                self.available_hatchback_spaces -= 1
            else:
                if self.available_suv_spaces > 0:  # Park hatchback in SUV space if available
                    print("No available hatchback spaces. Car parked in SUV space (hatchback rate applies).")
                    car.car_type = "HATCHBACK"  # Adjust type for fee calculation
                    self.available_suv_spaces -= 1
                else:
                    print("Parking lot is full.")
                    self.parked_cars.pop()  # Remove car from parked_cars if no space
        else:
            print(f"Invalid car type: {car_type}")

    def exit_car(self, car_number, current_time):
        for car in self.parked_cars.copy():  # Iterate over a copy to avoid index issues
            if car.car_type == str(car_number):  # Match car type with number
                car.calculate_fee(current_time)
                print(f"Car number {car_number} (type: {car.car_type}) - Parking fee: ₹{car.parking_fee:.2f}")
                self.parked_cars.remove(car)
                if car.car_type == "SUV":
                    self.available_suv_spaces += 1
                else:
                    self.available_hatchback_spaces += 1
                return  # Exit loop after finding the car

        print(f"Car number {car_number} not found.")

    def view_parked_cars(self):
        if not self.parked_cars:
            print("No cars parked currently.")
            return

        print("Parked Cars:")
        for car in self.parked_cars:
            print(f"- Car number: {car.car_type} (Entry time: {car.entry_time:%H:%M:%S})")

    def set_initial_spaces(self, hatchback_spaces, suv_spaces):
        self.available_hatchback_spaces = hatchback_spaces
        self.available_suv_spaces = suv_spaces
        print(f"Parking lot initialized with {hatchback_spaces} hatchback spaces and {suv_spaces} SUV spaces.")


if __name__ == "__main__":
    parking_manager = ParkingManager()

    # Set initial parking spaces (adjust numbers as needed)
    parking_manager.set_initial_spaces(10, 5)

    while True:
        print("\nMenu:")
        print("1. Park a car")
        print("2. Exit car")
        print("3. View parked cars")
        print("4. Set initial parking spaces")
        print("5. Exit program")

        choice = input("Enter your choice (1-5): ")

        if choice == '1':
            # Code for parking a car
            car_type = input("Enter car type (SUV/HATCHBACK): ")
            current_time = datetime.datetime.now()  # Get current time
            parking_manager.park_car(car_type, current_time)
        elif choice == '2':
            # Code for exiting a car
            car_number = input("Enter car number: ")
            current_time = datetime.datetime.now()
            parking_manager.exit_car(car_number, current_time)
        elif choice == '3':
            # Code for viewing parked cars
            parking_manager.view_parked_cars()
        elif choice == '4':
            # Code for setting initial parking spaces
            try:
                hatchback_spaces = int(input("Enter number of hatchback spaces: "))
                suv_spaces = int(input("Enter number of SUV spaces: "))
                parking_manager.set_initial_spaces(hatchback_spaces, suv_spaces)
            except ValueError:
                print("Invalid input. Please enter integers for parking spaces.")
        elif choice == '5':
            print("Exiting program...")
            break
        else:
            print("Invalid choice. Please enter a number between 1 and 5.")
