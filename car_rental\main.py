from service.CarService import CarService
from service.CustomerService import CustomerService
from service.RideService import RideService
import asyncio
class Main:
    def __init__(self):
        self.car_service = CarService()
        self.customer_service = CustomerService()
        # RideService needs repositories as parameters
        self.ride_service = RideService(
            ride_repository=[],  # List to store rides
            customer_repository=self.customer_service.customers,  # Customer dictionary
            car_repository=self.car_service.cars  # Car dictionary
        )

async def run_main():
    try:
        main = Main()

        # Add cars
        print("=== Adding Cars ===")
        print(await main.car_service.add_car("KA-01-12345", (1, 1)))
        print(await main.car_service.add_car("KA-01-12346", (2, 2)))
        print(await main.car_service.add_car("KA-01-12347", (3, 3)))

        # Add customers
        print("\n=== Adding Customers ===")
        print(await main.customer_service.add_customer("<PERSON>", "<EMAIL>", (0, 0)))
        print(await main.customer_service.add_customer("Bob", "<EMAIL>", (0, 0)))
        print(await main.customer_service.add_customer("Charlie", "<EMAIL>", (0, 0)))

        print("\n=== Car Rental System Initialized ===")

        # Search for available cars
        print("\n=== Searching Available Cars ===")
        available_cars = await main.ride_service.search_cars("<EMAIL>")
        print(f"Available cars for Alice: {[car.license_plate for car in available_cars]}")

        # Book a ride
        print("\n=== Booking Ride ===")
        ride = await main.ride_service.book_ride("<EMAIL>", "KA-01-12345", (0, 0), (10, 10))
        print(f"Ride booked: {ride}")
        print(f"Initial bill amount: {ride.bill_amount}")

        # End the ride
        print("\n=== Ending Ride ===")
        completed_ride = await main.ride_service.end_ride("<EMAIL>")
        print(f"Completed ride: {completed_ride}")
        print(f"Final bill amount: {completed_ride.bill_amount}")

        # Check earnings
        print("\n=== Checking Earnings ===")
        print(f"Total system earnings: {await main.ride_service.get_total_earnings()}")
        print(f"Car KA-01-12345 earnings: {await main.car_service.get_car_earnings('KA-01-12345')}")

        # Update customer information
        print("\n=== Updating Customer Information ===")
        print(await main.customer_service.update_name("<EMAIL>", "Alicia"))
        print(await main.customer_service.update_location("<EMAIL>", (1, 1)))

        print("\n=== Final Customer Info ===")
        customer = main.customer_service.customers["<EMAIL>"]
        print(f"Updated Customer: {customer}")

    except Exception as e:
        print(f"Error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(run_main())