package clearfit;

import java.util.*;

public class Main {
    public static void main(String[] args) {
        ClearFitService service = new ClearFitService();

        service.registerUser("U1", "Alice");

        List<TimeSlot> timings = Arrays.asList(new TimeSlot(6, 22));
        List<String> workoutTypes = Arrays.asList("Yoga", "Cardio");
        Map<String, List<WorkoutSlot>> workoutSchedules = new HashMap<>();
        workoutSchedules.put("Yoga", new ArrayList<>(Arrays.asList(
        new WorkoutSlot(5, 5, 1, new TimeSlot(7, 8), new HashSet<>())              )));
        service.registerCenter("Koramangala", timings, workoutTypes, workoutSchedules);
        service.addWorkoutSlot("Koramangala", "Yoga", new TimeSlot(7, 8), 5);

        boolean booked = service.bookSlot("U1", "Koramangala", "Yoga", new TimeSlot(7, 8));

        System.out.println("Booking Successful: " + booked);
        System.out.println("User Bookings: " + service.getUsers().get("U1").getBookings());
    }
}
