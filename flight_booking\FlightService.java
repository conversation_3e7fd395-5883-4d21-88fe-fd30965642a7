package flight_booking;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class FlightService {
    private HashMap<String, Flights> flights;

    public FlightService(){
        this.flights = new HashMap<>();
    }

    public String addFlight(Flights flight){
        if (flights.containsKey(flight.getFlightNumber())){
            throw new IllegalArgumentException("Flight Number already exists: " + flight.getFlightNumber());
        }
        flights.put(flight.getFlightNumber(), flight);
        return String.format("Flight Number successfully added: %d", flight.getFlightNumber());
    }

    public List<Flights> searchFlight(String criteria, String value){
        List<Flights> res = new ArrayList<>();
        for (Flights flight : flights.values()){
            switch (criteria.toLowerCase()){
                case "source":
                if (flight.getSource().equalsIgnoreCase(value)) res.add(flight);
                break;
                case "destination":
                if (flight.getDestination().equalsIgnoreCase(value)) res.add(flight);
                break;
                case "date":
                if (flight.getDate().equalsIgnoreCase(value)) res.add(flight);
                break;
                default:
                throw new IllegalArgumentException("Please choose value among source, destination and date");
            }
        }
        return res;
    }

    public void displayFlights(){
        if (flights.isEmpty()){
            System.out.println("No available flights");
            return;
        }
        for (Flights flight : flights.values()){
            System.out.println(flight);
        }
    }

    public boolean exists(String flightNumber) {
        return flights.containsKey(flightNumber);
    }

    public Flights getFlight(String flightNumber) {
        return flights.get(flightNumber);
    }
}
