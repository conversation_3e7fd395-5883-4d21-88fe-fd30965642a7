#!/usr/bin/env python3
"""
Simple dashboard to view MySQL diagnostics results
"""

from mysql_query_diagnostics import MySQLQueryDiagnostics, DatabaseConfig
import json
import time
import os

def clear_screen():
    """Clear the console screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_dashboard_header():
    """Print dashboard header"""
    print("=" * 80)
    print("🔍 MYSQL QUERY DIAGNOSTICS DASHBOARD")
    print("=" * 80)
    print(f"⏰ Last Updated: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

def print_quick_stats(diagnostics_tool, db_name):
    """Print quick database statistics"""
    try:
        info = diagnostics_tool.get_database_info(db_name)
        if info:
            print(f"\n📊 DATABASE INFO - {db_name.upper()}")
            print(f"   MySQL Version: {info['version']}")
            print(f"   Database Size: {info['size_mb']} MB")
            print(f"   Table Count: {info['table_count']}")
    except Exception as e:
        print(f"   Error getting database info: {e}")

def run_sample_queries(diagnostics_tool, db_name):
    """Run sample queries and display results"""
    
    sample_queries = [
        ("Simple SELECT", "SELECT COUNT(*) as user_count FROM users"),
        ("JOIN Query", "SELECT u.name, COUNT(o.id) as orders FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id LIMIT 5"),
        ("Performance Test", "SELECT * FROM users WHERE email LIKE '%@gmail.com'"),
    ]
    
    print(f"\n🔍 QUERY ANALYSIS RESULTS")
    print("-" * 80)
    
    for query_name, query in sample_queries:
        try:
            result = diagnostics_tool.diagnose_query(db_name, query)
            if result:
                print(f"\n📝 {query_name}")
                print(f"   Query: {query[:50]}{'...' if len(query) > 50 else ''}")
                print(f"   ⚡ Execution Time: {result.execution_time:.4f}s")
                print(f"   📊 EXPLAIN Rows: {len(result.explain_plan)}")
                
                if result.warnings:
                    print(f"   ⚠️  Warnings: {len(result.warnings)}")
                    for warning in result.warnings[:2]:  # Show first 2 warnings
                        print(f"      • {warning}")
                
                if result.recommendations:
                    print(f"   💡 Recommendations: {len(result.recommendations)}")
                    for rec in result.recommendations[:1]:  # Show first recommendation
                        print(f"      • {rec}")
                
                print("-" * 40)
        except Exception as e:
            print(f"   ❌ Error running {query_name}: {e}")

def interactive_query_mode(diagnostics_tool, db_name):
    """Interactive mode for custom queries"""
    print(f"\n🎯 INTERACTIVE QUERY MODE")
    print("Enter your SQL query (or 'quit' to exit):")
    
    while True:
        try:
            query = input("\n🔍 SQL> ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            print("\nAnalyzing query...")
            result = diagnostics_tool.diagnose_query(db_name, query)
            
            if result:
                print(f"\n⚡ Execution Time: {result.execution_time:.4f} seconds")
                
                # Show EXPLAIN plan summary
                print(f"📊 EXPLAIN Plan Summary:")
                for i, plan in enumerate(result.explain_plan, 1):
                    print(f"   {i}. Table: {plan.table}, Type: {plan.type}, Rows: {plan.rows}")
                
                # Show warnings
                if result.warnings:
                    print(f"\n⚠️  Warnings ({len(result.warnings)}):")
                    for warning in result.warnings:
                        print(f"   • {warning}")
                
                # Show recommendations
                if result.recommendations:
                    print(f"\n💡 Recommendations ({len(result.recommendations)}):")
                    for rec in result.recommendations:
                        print(f"   • {rec}")
            else:
                print("❌ Failed to analyze query")
                
        except KeyboardInterrupt:
            print("\n\nExiting interactive mode...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main dashboard function"""
    
    # Load configuration
    try:
        with open('mysql_test_config.json', 'r') as f:
            config_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return
    
    # Initialize diagnostics tool
    diagnostics_tool = MySQLQueryDiagnostics()
    
    # Add database configuration
    for db_config in config_data['databases']:
        config = DatabaseConfig(**db_config)
        diagnostics_tool.add_database(config)
    
    db_name = list(diagnostics_tool.databases.keys())[0]
    
    try:
        while True:
            clear_screen()
            print_dashboard_header()
            print_quick_stats(diagnostics_tool, db_name)
            run_sample_queries(diagnostics_tool, db_name)
            
            print(f"\n🎮 OPTIONS:")
            print("1. Refresh dashboard")
            print("2. Interactive query mode")
            print("3. Exit")
            
            choice = input("\nSelect option (1-3): ").strip()
            
            if choice == '1':
                continue
            elif choice == '2':
                interactive_query_mode(diagnostics_tool, db_name)
            elif choice == '3':
                break
            else:
                print("Invalid option. Press Enter to continue...")
                input()
    
    except KeyboardInterrupt:
        print("\n\nDashboard stopped.")
    
    finally:
        diagnostics_tool.disconnect_all()
        print("✅ All connections closed.")

if __name__ == "__main__":
    main()
