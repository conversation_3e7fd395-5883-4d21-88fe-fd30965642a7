class RoundRobin(object):
    def __init__(self, servers):
        self.servers = servers
        self.counter = 0

    def get_next_server(self):
        server = self.servers[self.counter]
        self.counter = (self.counter + 1) % len(self.servers)

        return server
    
servers = ["server 1", "server 2", "server 3"]
load_balancer = RoundRobin(servers)

for i in range(10):
    server = load_balancer.get_next_server()
    print(f"Request {i + 1} is handled by {server}")
