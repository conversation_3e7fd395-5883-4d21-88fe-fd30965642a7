"""
Example usage of OpenCV-based video concatenation.

This script demonstrates how to use the OpenCV video concatenation functionality.
"""

from video_concat_opencv import concatenate_videos_opencv, get_video_files, get_video_info
import os

def example_basic_concatenation():
    """Example: Basic video concatenation with a list of files."""
    print("Example 1: Basic concatenation with OpenCV")
    print("-" * 45)
    
    # List of video files to concatenate (replace with your actual file paths)
    video_list = [
        "video1.mp4",
        "video2.mp4", 
        "video3.mp4"
    ]
    
    output_path = "combined_video_opencv.mp4"
    
    # Check if files exist before attempting concatenation
    existing_files = [f for f in video_list if os.path.exists(f)]
    
    if existing_files:
        print("Found videos:")
        for video in existing_files:
            info = get_video_info(video)
            if info:
                print(f"  - {video}: {info['width']}x{info['height']}, {info['duration']:.1f}s")
        
        success = concatenate_videos_opencv(existing_files, output_path, target_fps=30)
        if success:
            print(f"✅ Successfully created: {output_path}")
        else:
            print("❌ Concatenation failed")
    else:
        print("⚠️  No video files found. Please update the video_list with actual file paths.")

def example_directory_concatenation():
    """Example: Concatenate all videos from a directory."""
    print("\nExample 2: Directory concatenation with OpenCV")
    print("-" * 48)
    
    directory = "sample_videos"  # Replace with your directory path
    
    if os.path.isdir(directory):
        video_files = get_video_files(directory)
        
        if video_files:
            print(f"Found {len(video_files)} videos in '{directory}':")
            for i, file in enumerate(video_files, 1):
                info = get_video_info(file)
                if info:
                    print(f"  {i}. {os.path.basename(file)} ({info['width']}x{info['height']}, {info['duration']:.1f}s)")
                else:
                    print(f"  {i}. {os.path.basename(file)} (could not read info)")
            
            output_path = os.path.join(directory, "all_videos_combined_opencv.mp4")
            success = concatenate_videos_opencv(video_files, output_path, target_fps=30)
            
            if success:
                print(f"✅ Successfully created: {output_path}")
            else:
                print("❌ Concatenation failed")
        else:
            print(f"No video files found in '{directory}'")
    else:
        print(f"⚠️  Directory '{directory}' does not exist. Please create it and add some video files.")

def create_test_videos():
    """Create simple test videos for demonstration."""
    print("\nCreating test videos for demonstration...")
    print("-" * 45)
    
    try:
        import cv2
        import numpy as np
        
        # Create a test directory
        test_dir = "test_videos"
        os.makedirs(test_dir, exist_ok=True)
        
        # Video properties
        width, height = 640, 480
        fps = 30
        duration = 3  # seconds
        frame_count = fps * duration
        
        colors = [
            (255, 0, 0),    # Blue
            (0, 255, 0),    # Green
            (0, 0, 255),    # Red
        ]
        
        video_names = ["blue_video.mp4", "green_video.mp4", "red_video.mp4"]
        
        for i, (color, name) in enumerate(zip(colors, video_names)):
            video_path = os.path.join(test_dir, name)
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
            
            print(f"Creating {name}...")
            
            for frame_num in range(frame_count):
                # Create a frame with solid color and frame number
                frame = np.full((height, width, 3), color, dtype=np.uint8)
                
                # Add text showing frame number and video info
                text = f"Video {i+1} - Frame {frame_num+1}"
                cv2.putText(frame, text, (50, height//2), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                out.write(frame)
            
            out.release()
            print(f"✅ Created: {video_path}")
        
        print(f"\n📁 Test videos created in '{test_dir}' directory")
        print("You can now run the concatenation examples!")
        
        return test_dir
        
    except Exception as e:
        print(f"❌ Error creating test videos: {e}")
        return None

def demo_with_test_videos():
    """Demonstrate concatenation with created test videos."""
    test_dir = create_test_videos()
    
    if test_dir:
        print(f"\nDemonstrating concatenation with test videos...")
        print("-" * 50)
        
        video_files = get_video_files(test_dir)
        if video_files:
            output_path = os.path.join(test_dir, "demo_concatenated.mp4")
            success = concatenate_videos_opencv(video_files, output_path, target_fps=30)
            
            if success:
                print(f"🎉 Demo completed! Check out: {output_path}")
            else:
                print("❌ Demo failed")

if __name__ == "__main__":
    print("🎬 OpenCV Video Concatenation Examples")
    print("=" * 55)
    
    # Run examples
    example_basic_concatenation()
    example_directory_concatenation()
    
    # Create and demonstrate with test videos
    demo_with_test_videos()
    
    print("\n" + "=" * 55)
    print("💡 Tips for OpenCV video concatenation:")
    print("1. OpenCV is more reliable than moviepy for basic concatenation")
    print("2. All videos will be resized to match the first video's dimensions")
    print("3. You can specify target FPS (default: 30)")
    print("4. Supported formats: MP4, AVI, MOV, MKV, WMV, FLV")
    print("5. Use the main video_concat_opencv.py script for interactive usage")
