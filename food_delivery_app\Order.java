import java.util.*;

public class Order {
    private static int counter = 1;
    private String orderId;
    private Customer customer;
    private Restaurant restaurant;
    private Map<Item, Integer> items;
    private String status;

    public Order(Customer customer, Restaurant restaurant, Map<Item, Integer> items){
        this.orderId = "ORDER" + counter++;
        this.customer = customer;
        this.restaurant = restaurant;
        this.items = items;
        this.status = "PLACED";
    }

    public String getOrderId() { return orderId; }
    public Customer getCustomer() { return customer; }
    public Restaurant getRestaurant() { return restaurant; }
    public Map<Item, Integer> getItems() { return items; }
    public String getStatus() { return status; }

    public void cancel() {
        if (status.equals("PLACED")){
            status = "CANCELLED";

            items.forEach((item, qty) -> item.reduceQuantity(-qty));
        }
        else{
            throw new IllegalStateException("Order cannot be cancelled.");
        }
    }

    @Override
    public String toString(){
        StringBuilder sb = new StringBuilder();
        sb.append("OrderId: ").append(orderId).append(", Status: ").append(status).append("\n");
        sb.append("Restaurant: ").append(restaurant.getName()).append("\n");
        sb.append("Items: \n");
        items.forEach((item, qty) -> sb.append(item.getName()).append(" * ").append(qty).append("\n"));
        return sb.toString();
    }

}
