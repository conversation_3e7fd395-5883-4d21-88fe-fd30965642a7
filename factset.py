# moderator - 1 - 90 random nos.
# when we reach the 90th number we will end our code.

# import random

# def generate_numbers():
#     nums = []
#     # num = random.randint(1, 5)
#     # print(num)
#     def get_num(nums):
#         l, r = 1, len(nums) - 1
#         mid = (l + r) // 2
#         num = nums[mid]
#         nums.pop(mid)
#         return num

#     arr = [i for i in range(1, 91)]

#     while len(nums) < 90:
#         num = get_num(arr)
#         if num not in nums:
#             nums.append(num)
            
#     print(nums)
    
# generate_numbers()

# design a web service which will give a short url for a given url

# input - url , output - short url

# short url will be of 16 char len

url_map = {}

def fetch_url(short_url):
    if short_url in url_map:
        return {"url": url_map[short_url]}
    return {"URL not found"}

def get_short_url(url):
    # url is manipulated
    short_url = str(hash(url))

    if len(short_url) < 16:
        print(short_url)
        short_url = short_url + (16 - len(short_url)) * '0'
    elif len(short_url) > 16:
        while len(short_url) > 16:
            short_url = str(hash(short_url))
    
    url_map[short_url] = url

    return short_url

url_var = get_short_url("www.com")
print(url_var)
print(fetch_url(url_var))

