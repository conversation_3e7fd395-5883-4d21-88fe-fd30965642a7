
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL Query Diagnostics Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .database-section {
            margin: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .database-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .database-name {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
        .execution-time {
            color: #28a745;
            font-weight: bold;
            margin: 5px 0;
        }
        .query-section {
            padding: 20px;
        }
        .query-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .explain-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .explain-table th,
        .explain-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .explain-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .explain-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .warnings {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .warnings h3 {
            color: #856404;
            margin-top: 0;
        }
        .warnings ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .warnings li {
            color: #856404;
            margin: 5px 0;
        }
        .recommendations {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .recommendations h3 {
            color: #0c5460;
            margin-top: 0;
        }
        .recommendations ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .recommendations li {
            color: #0c5460;
            margin: 5px 0;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            text-align: right;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 MySQL Query Diagnostics Report</h1>
            <p>Generated on 2025-12-11 19:58:44</p>
        </div>

        <div class="database-section">
            <div class="database-header">
                <h2 class="database-name">📊 TEST_DATABASE</h2>
                <div class="execution-time">⚡ Execution Time: 0.0011 seconds</div>
                <div class="timestamp">🕒 2025-12-11T19:54:22.524483</div>
            </div>
            
            <div class="query-section">
                <h3>📝 Query</h3>
                <div class="query-box">SELECT u.name, o.amount FROM users u JOIN orders o ON u.id = o.user_id WHERE o.status = 'completed'</div>
                
                <h3>📋 EXPLAIN Plan</h3>
                <table class="explain-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Select Type</th>
                            <th>Table</th>
                            <th>Type</th>
                            <th>Key</th>
                            <th>Rows</th>
                            <th>Extra</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>1</td>
                            <td>SIMPLE</td>
                            <td>u</td>
                            <td>ALL</td>
                            <td>None</td>
                            <td>1</td>
                            <td></td>
                        </tr>

                        <tr>
                            <td>1</td>
                            <td>SIMPLE</td>
                            <td>o</td>
                            <td>ref</td>
                            <td>idx_user_id</td>
                            <td>1</td>
                            <td>Using where</td>
                        </tr>

                    </tbody>
                </table>

                <div class="warnings">
                    <h3>⚠️ Warnings</h3>
                    <ul>
                        <li>Full table scan on table 'u' (rows: 1)</li>
                        <li>No index used for table 'u'</li>

                    </ul>
                </div>

                <div class="recommendations">
                    <h3>💡 Recommendations</h3>
                    <ul>
                        <li>Consider adding an index on table 'u'</li>
                        <li>Consider adding appropriate indexes for table 'u'</li>

                    </ul>
                </div>

            </div>
        </div>

    </div>
</body>
</html>
