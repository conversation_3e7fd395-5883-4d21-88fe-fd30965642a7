# MySQL Cross-Database Query Diagnostics Tool

A comprehensive Python tool for analyzing MySQL query performance across multiple databases using EXPLAIN plans and execution timing.

## Features

- **Cross-Database Analysis**: Run the same query across multiple MySQL databases and compare performance
- **EXPLAIN Plan Analysis**: Automatically parse and analyze MySQL EXPLAIN plans
- **Performance Warnings**: Detect common performance issues like full table scans, filesorts, and temporary tables
- **Intelligent Recommendations**: Get actionable suggestions for query optimization
- **Execution Timing**: Measure actual query execution time
- **Export Results**: Save diagnostics results to JSON format
- **CLI Interface**: Easy-to-use command-line interface
- **Database Information**: Get MySQL version, database size, and table counts

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure you have MySQL databases accessible with appropriate read permissions.

## Quick Start

### 1. Create Configuration File

Generate a sample configuration file:
```bash
python mysql_diagnostics_cli.py --create-sample-config
```

This creates `mysql_config.json` with the following structure:
```json
{
  "databases": [
    {
      "name": "production",
      "host": "prod-mysql.example.com",
      "port": 3306,
      "database": "ecommerce",
      "user": "readonly_user",
      "password": "secure_password"
    },
    {
      "name": "staging",
      "host": "staging-mysql.example.com",
      "port": 3306,
      "database": "ecommerce",
      "user": "readonly_user",
      "password": "secure_password"
    }
  ]
}
```

### 2. Run Query Analysis

Analyze a query across all configured databases:
```bash
python mysql_diagnostics_cli.py -c mysql_config.json -q "SELECT * FROM users WHERE email LIKE '%@gmail.com'"
```

Analyze a query from a file:
```bash
python mysql_diagnostics_cli.py -c mysql_config.json -f query.sql
```

Analyze specific databases only:
```bash
python mysql_diagnostics_cli.py -c mysql_config.json -q "SELECT * FROM orders" -d production staging
```

Compare performance across databases:
```bash
python mysql_diagnostics_cli.py -c mysql_config.json -q "SELECT * FROM products" --compare
```

Export results to JSON:
```bash
python mysql_diagnostics_cli.py -c mysql_config.json -q "SELECT * FROM users" -e results.json
```

## Programmatic Usage

```python
from mysql_query_diagnostics import MySQLQueryDiagnostics, DatabaseConfig

# Initialize tool
diagnostics = MySQLQueryDiagnostics()

# Add database configuration
config = DatabaseConfig(
    name="production",
    host="localhost",
    port=3306,
    database="ecommerce",
    user="readonly_user",
    password="your_password"
)
diagnostics.add_database(config)

# Analyze query
query = "SELECT * FROM users WHERE created_at > '2024-01-01'"
result = diagnostics.diagnose_query("production", query)

# Print report
diagnostics.print_diagnostics_report(result)

# Clean up
diagnostics.disconnect_all()
```

## Output Example

```
================================================================================
QUERY DIAGNOSTICS REPORT - production
================================================================================
Timestamp: 2024-12-11T10:30:45.123456
Execution Time: 0.0234 seconds

Query:
SELECT u.id, u.name, o.total_amount FROM users u JOIN orders o ON u.id = o.user_id

EXPLAIN PLAN
--------------------------------------------------------------------------------
ID  Type         Table           Key             Rows     Extra               
--------------------------------------------------------------------------------
1   <USER>       <GROUP>               PRIMARY         1000     
1   SIMPLE       o               user_id_idx     5        Using index         

WARNINGS
--------------------------------------------------------------------------------
1. High row count (1000) for table 'u'

RECOMMENDATIONS
--------------------------------------------------------------------------------
1. Consider optimizing query or adding WHERE clause for table 'u'
================================================================================
```

## Detected Issues and Recommendations

The tool automatically detects:

- **Full Table Scans** (`type = 'ALL'`)
- **High Row Counts** (> 10,000 rows)
- **Filesort Operations** (`Using filesort` in Extra)
- **Temporary Tables** (`Using temporary` in Extra)
- **Missing Indexes** (no key used)
- **Low Selectivity** (< 10% filtered with high row count)

## Files

- `mysql_query_diagnostics.py` - Main diagnostics tool
- `mysql_diagnostics_cli.py` - Command-line interface
- `example_usage.py` - Example usage script
- `requirements.txt` - Python dependencies
- `README.md` - This documentation
