import datetime


class Meeting:
    def __init__(self, meeting_id, start_time, end_time, no_of_attendees):
        self.meeting_id = meeting_id
        self.start_time = start_time
        self.end_time = end_time
        self.no_of_attendees = no_of_attendees

class MeetingRoom:
    def __init__(self, room_id, capacity):
        self.room_id = room_id
        self.capacity = capacity
        self.bookings = []
    
    def is_available(self, start_time, end_time):
        for booking in self.bookings:
            if not (end_time <= booking.start_time or start_time >= booking.end_time):
                return False
        return True

    def book_meeting(self, meeting):
        if self.is_available(meeting.start_time, meeting.end_time) and len(meeting.attendees) <= self.capacity:
            self.bookings.append(meeting)
            return True
        return False

class MeetingScheduler:
    def __init__(self):
        self.rooms = []
        self.meetings = []
        self.next_meeting_id = 1

    def add_room(self, room_id, capacity):
        self.rooms.append(MeetingRoom(room_id, capacity))

    def book_meeting(self, room_id, start_time, end_time, attendees):
        room = next((r for r in self.rooms if r.room_id == room_id), None)

        if room is None:
            return f"{room_id} does not exist."
        
        meeting = Meeting(self.next_meeting_id, start_time, end_time, attendees)
        if room.book_meeting(meeting):
            self.meetings.append(meeting)
            self.next_meeting_id += 1
            self.send_notifications(attendees, room_id, start_time, end_time)
            return f"Meeting booked from {start_time} to {end_time}"
        
        else:
            return f"Room {room_id} does not have enough capacity or can't be alloted for requested time."
        
    def send_notifications(self, attendees, room_id, start_time, end_time):
        for attendee in attendees:
            print(f"Notification sent to {attendee}: Meeting scheduled in room {room_id} from {start_time} to {end_time}")

    
if __name__=="__main__":
    scheduler = MeetingScheduler()