from typing import Dict
from BNPL.models.User import User

class UserService:
    def __init__(self):
        self.users: Dict[str, User] = {}

    def register_user(self, user_id: str, credit_limit: float) -> User:
        if user_id in self.users:
            raise Exception("User already exists")
        user = User(user_id=user_id, credit_limit=credit_limit)
        self.users[user_id] = user
        return user

    def get_user(self, user_id: str) -> User:
        if user_id not in self.users:
            raise Exception("User not found")
        return self.users[user_id]

    def _get_user(self, user_id: str) -> User:
        """Deprecated: Use get_user instead"""
        return self.get_user(user_id)
    