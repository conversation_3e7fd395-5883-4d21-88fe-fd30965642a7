from datetime import date
from typing import List, Tuple
from bnpl_system.models.Order import Order
from bnpl_system.PaymentMethods import PaymentMethod

class OrderService:
    def __init__(self, user_service, item_service):
        self.user_service = user_service
        self.item_service = item_service
        self._next_order_id = 1

    def buy(self, user_id: str, cart: List[Tuple[str, int]], 
                   payment_method: str, purchase_date: str) -> Order:

        payment_method_enum = PaymentMethod.BNPL if payment_method == "BNPL" else PaymentMethod.PREPAID
        purchase_date_obj = date.fromisoformat(purchase_date) if isinstance(purchase_date, str) else purchase_date
        
        user = self.user_service.get_user(user_id)
        
        total_amount = sum(self.item_service.item_repository[name].price * qty for name, qty in cart)
        if payment_method_enum == PaymentMethod.BNPL:
            if total_amount > user.remaining_limit:
                raise ValueError("Not enough BNPL credit")

        for name, qty in cart:
            self.item_service.item_repository[name].count -= qty
        order_id = str(self._next_order_id)
        order = Order(
            order_id=order_id,
            user_id=user_id,
            items=cart,
            total_amount=total_amount,
            payment_method=payment_method_enum,
            purchase_date=purchase_date_obj
        )
        
        if payment_method_enum == PaymentMethod.BNPL:
            user.remaining_limit -= total_amount
        
        user.orders.append(order)
        self._next_order_id += 1
        
        print(f"\nOrder placed successfully. ID = {order.order_id}, Amount = {total_amount}, By = {user_id}")
        return order

    def view_dues(self, user_id: str, upto_date: str) -> None:
        user = self.user_service.get_user(user_id)
        upto_date_obj = date.fromisoformat(upto_date) if isinstance(upto_date, str) else upto_date
        
        dues = [o for o in user.orders if o.is_due(upto_date_obj)]
        dues.sort(key=lambda o: o.purchase_date)

        print(f"\nDUES for {user_id} upto {upto_date}")
        if not dues:
            print("\nNo dues.")
            return

        for o in dues:
            print(
                f"OrderID={o.order_id} | Amount={o.total_amount} | "
                f"Date={o.purchase_date} | Status={o.status_on(upto_date_obj)}"
            )

    def clear_dues(self, user_id: str, order_ids: List[str], clearing_date: str) -> None:
        user = self.user_service.get_user(user_id)
        clearing_date_obj = date.fromisoformat(clearing_date) if isinstance(clearing_date, str) else clearing_date

        for oid in order_ids:
            orders = {o.order_id: o for o in user.orders}
            order = orders.get(oid)
            if not order:
                print(f"\nOrder {oid} not found")
                continue
            if order.cleared or order.payment_method != PaymentMethod.BNPL:
                print(f"No BNPL dues for Order {oid}")
                continue

            order.cleared = True
            order.cleared_date = clearing_date_obj
            user.remaining_limit += order.total_amount
            print(f"\nDues cleared for order {oid}")

    def order_status(self, user_id: str) -> None:
        user = self.user_service.get_user(user_id)
        print(f"\nORDER HISTORY for {user_id}")

        if not user.orders:
            print("No orders found.")
            return

        for o in sorted(user.orders, key=lambda x: x.purchase_date):
            print(
                f"OrderID={o.order_id} | Date={o.purchase_date} | "
                f"Payment={o.payment_method} | Amount={o.total_amount} | Cleared={o.cleared}"
            )

        print(f"Current BNPL Credit Left: {user.remaining_limit}")

    def blacklist_user(self):
        pass # To be implemented