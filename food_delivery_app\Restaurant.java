import java.util.*;
public class Restaurant extends User{
    public Map<String, Item> catalog = new HashMap<>();

    public Restaurant(String name, String id){
        super(name, id);
    }

    public void addItem(String itemId, String name, double price, int quantity){
        catalog.put(itemId, new Item(itemId, name, price, quantity));
    }

    public List<Item> searchItem(String itemName){
        List<Item> res = new ArrayList<>();

        for (Item item: catalog.values()){
            if (item.getName().toLowerCase().contains(itemName.toLowerCase())){
                res.add(item);
            }
        }

        res.sort((a,b) -> Double.compare(a.getPrice(), b.getPrice()));
        return res;
    }

    public Item getItemById(String itemId){
        return catalog.get(itemId);
    }

    public Map<String, Item> getCatalog(){
        return catalog;
    }
}