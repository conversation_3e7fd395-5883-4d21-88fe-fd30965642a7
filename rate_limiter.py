from threading import Lock
import time

class TokenBucket:
  """
  A simple token bucket implementation for rate limiting.
  """

  def __init__(self, capacity, fill_rate, time_unit=1):
    """
    Initialize the token bucket.

    Args:
      capacity: The maximum number of tokens the bucket can hold.
      fill_rate: The rate at which tokens are added to the bucket (tokens per time unit).
      time_unit: The time unit used for the fill rate (default: 1 second).
    """
    self.capacity = capacity
    self.fill_rate = fill_rate
    self.time_unit = time_unit
    self.last_fill_time = time.time()
    self.lock = Lock()
    self._tokens = capacity  # Initialize with full capacity

  def get_token(self):
    """
    Attempts to get a token from the bucket.

    Returns:
      True if a token is available, False otherwise.
    """
    with self.lock:
      current_time = time.time()
      # Refill the bucket based on elapsed time
      tokens_to_fill = (current_time - self.last_fill_time) * self.fill_rate
      self._tokens = min(self.capacity, self._tokens + tokens_to_fill)
      self.last_fill_time = current_time

      if self._tokens > 0:
        self._tokens -= 1
        return True
      else:
        return False

  def can_consume(self, amount=1):
    """
    Checks if the bucket has enough tokens to allow consumption.

    Args:
      amount: The number of tokens to consume (default: 1).

    Returns:
      True if enough tokens are available, False otherwise.
    """
    return self.get_token() * amount

# Example usage
bucket = TokenBucket(capacity=10, fill_rate=2)

if bucket.can_consume():
  # Process request
  print("Request processed")
else:
  print("Request dropped due to rate limit")
