from datetime import date
from typing import List, Tuple, Optional
from PaymentMethod import PaymentMethod

class Order:
    order_id: int
    user_id: str
    items: List[Tuple[str, int]] 
    total_amount: float
    payment_method: PaymentMethod
    purchase_date: date
    cleared: bool = False
    cleared_date: Optional[date] = None

    def is_due(self, on_date: date) -> bool:
        return (
            self.payment_method == PaymentMethod.BNPL
            and not self.cleared
            and self.purchase_date <= on_date
        )

    def status_on(self, on_date: date) -> str:
        if not self.is_due(on_date):
            return "CLEARED"
        overdue = (on_date - self.purchase_date).days
        if overdue > 30: return "DELAYED"
        return "PENDING"
