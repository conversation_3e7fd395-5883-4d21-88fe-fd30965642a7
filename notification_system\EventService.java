package notification_system;
import java.util.*;

public class EventService {
    // Map: userId -> Map<EventType, Set<ChannelType>>
    private final Map<Integer, Map<EventType, Set<ChannelType>>> userSubscriptions;

    public EventService() {
        this.userSubscriptions = new HashMap<>();
    }

    public synchronized String subscribeEvent(int userId, EventType eventType, ChannelType channelType) {
        userSubscriptions.putIfAbsent(userId, new HashMap<>());
        Map<EventType, Set<ChannelType>> eventMap = userSubscriptions.get(userId);
        eventMap.putIfAbsent(eventType, new HashSet<>());
        boolean added = eventMap.get(eventType).add(channelType);
        if (added) {
            return "User " + userId + " subscribed to " + eventType + " via " + channelType;
        } else {
            return "User " + userId + " already subscribed to " + eventType + " via " + channelType;
        }
    }

    public synchronized String unsubscribeEvent(int userId, EventType eventType, ChannelType channelType) {
        Map<EventType, Set<ChannelType>> eventMap = userSubscriptions.get(userId);
        if (eventMap != null && eventMap.containsKey(eventType)) {
            boolean removed = eventMap.get(eventType).remove(channelType);
            if (eventMap.get(eventType).isEmpty()) {
                eventMap.remove(eventType);
            }
            if (eventMap.isEmpty()) {
                userSubscriptions.remove(userId);
            }
            if (removed) {
                return "User " + userId + " unsubscribed from " + eventType + " via " + channelType;
            }
        }
        return "Subscription not found for user " + userId;
    }

    public synchronized String addChannel(int userId, EventType eventType, ChannelType channelType) {
        return subscribeEvent(userId, eventType, channelType);
    }

    public synchronized String removeChannel(int userId, EventType eventType, ChannelType channelType) {
        return unsubscribeEvent(userId, eventType, channelType);
    }

    public synchronized Map<Integer, Set<ChannelType>> getUsersToNotify(EventType eventType) {
        Map<Integer, Set<ChannelType>> result = new HashMap<>();
        for (Map.Entry<Integer, Map<EventType, Set<ChannelType>>> entry : userSubscriptions.entrySet()) {
            Map<EventType, Set<ChannelType>> eventMap = entry.getValue();
            Set<ChannelType> channels = eventMap.get(eventType);
            if (channels != null && !channels.isEmpty()) {
                result.put(entry.getKey(), new HashSet<>(channels));
            }
        }
        return result;
    }

    public synchronized Map<EventType, Set<ChannelType>> getUserSubscriptions(int userId) {
        Map<EventType, Set<ChannelType>> subs = userSubscriptions.get(userId);
        if (subs == null) {
            return Collections.emptyMap();
        }
        // Return a copy to avoid exposing internal state
        Map<EventType, Set<ChannelType>> copy = new HashMap<>();
        for (Map.Entry<EventType, Set<ChannelType>> entry : subs.entrySet()) {
            copy.put(entry.getKey(), new HashSet<>(entry.getValue()));
        }
        return copy;
    }

    /**
     * Get users to notify for a given event type and send notification message to each.
     * Returns a map of userId to the notification message sent.
     */
    public synchronized Map<Integer, String> notifyUsersForEvent(EventType eventType, String message) {
        Map<Integer, Set<ChannelType>> usersToNotify = getUsersToNotify(eventType);
        Map<Integer, String> notificationsSent = new HashMap<>();
        for (Map.Entry<Integer, Set<ChannelType>> entry : usersToNotify.entrySet()) {
            int userId = entry.getKey();
            Set<ChannelType> channels = entry.getValue();
            // Here, you can integrate with your notification sending logic per channel
            // For now, we just simulate sending by creating a message
            String notifyMsg = "Notification to user " + userId + " via " + channels + ": " + message;
            notificationsSent.put(userId, notifyMsg);
            // Optionally: actually send notification using a NotificationService
        }
        return notificationsSent;
    }
}
